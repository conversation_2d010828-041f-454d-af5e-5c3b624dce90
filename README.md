# 6DIMU Sensor Tools

本專案為一套基於 PySide6 的多感測器資料接收、記錄與分析工具，支援最多 4 組 6D IMU 感測器的即時連線、資料顯示、運動指標計算、數據轉發與深度分析。適合用於動作分析、康復訓練、運動科學研究等應用場景。

---

## 專案簡介

本工具提供完整的 IMU 數據處理解決方案，透過 Serial COM Port 連接 Gateway，由 Gateway 統一管理多組 BLE IMU 感測器。除了基本的資料接收與記錄功能外，還包含先進的運動指標計算、實時數據轉發、歷史數據分析、比較分析與趨勢分析等功能，為研究人員和開發者提供全方位的數據分析平台。

---

## 核心功能架構

### 🔴 實時監控系統
- **多感測器管理**：同時支援最多 4 組 IMU 感測器
- **即時數據顯示**：2x2 卡片式佈局，每個感測器獨立顯示
- **運動指標計算**：實時計算最大速度、爆發力、最大角度等指標
- **數據記錄與匯出**：支援 CSV 格式的數據記錄
- **數據轉發功能**：支援 UDP/TCP/WebSocket/MQTT 協議轉發

### 📊 數據分析系統
- **歷史數據回放**：載入 CSV 檔案進行回放分析
- **運動指標分析**：深度分析運動表現指標
- **時間範圍選擇**：精確控制分析時間段

### ⚖️ 比較分析系統
- **多檔案比較**：同時比較多個訓練記錄
- **指標對比**：詳細的數值比較與差異分析
- **多種比較模式**：整體比較、時間段比較、峰值比較

### 📈 趨勢分析系統
- **長期趨勢追蹤**：分析訓練進度與改善趨勢
- **統計摘要**：自動生成訓練統計報告
- **智能洞察**：提供數據驅動的訓練建議

---

## 詳細功能說明

### 1. 多感測器管理
   - 支援同時接收最多 4 組感測器的數據
   - 每組感測器皆可獨立選擇 MAC 地址、顯示與記錄資料
   - 動態 MAC 地址偵測，自動更新可用感測器列表
   - 獨立的電池電量監控與顯示

### 2. Serial COM Port 連線管理
   - 自動掃描並顯示可用的 Serial Port
   - 現代化的連線面板，支援一鍵連線/斷線
   - 自動處理意外斷線與重連機制
   - 即時連線狀態指示器

### 3. 資料透傳協議
   - **UART 傳送至 BLE 設備**（主機→Gateway→BLE）：
     ```
     NUS_TX:<MAC>,<DATA>;
     ```
     - `<MAC>`：目標設備 MAC 地址（AA:BB:CC:DD:EE:FF）
     - `<DATA>`：要發送的原始資料

   - **BLE 設備回傳資料至主機**（BLE→Gateway→主機）：
     ```
     NUS_RX:<MAC>,<DATA>;
     ```
     - `<MAC>`：來源設備 MAC 地址
     - `<DATA>`：BLE 設備回傳的原始資料

   - **雙向資料流機制**：
     - 主機發送 NUS_TX 指令，Gateway 解析後將 `<DATA>` 內容直接透過 NUS 寫入指定 MAC 的 BLE 設備
     - BLE 設備透過 NUS 通知/回應資料時，Gateway 自動將資料包裝為 `NUS_RX:<MAC>,<DATA>;` 並透過 UART 回傳給主機
     - 支援自動分包處理，確保完整資料接收

### 4. 統一數據顯示系統
   - **統一數據封包**：所有數據（6軸原始數據、歐拉角、四元數）都包含在同一個 22-byte 封包中
   - **無需模式切換**：不需要發送任何切換命令，所有數據同時可用
   - **UI 選擇顯示**：用戶可在 UI 中選擇顯示加速度、角速度、歐拉角等不同數據內容
   - **即時切換**：可隨時在不同數據視圖間切換，無需重新連接或配置
   - **完整數據記錄**：無論 UI 顯示什麼，所有數據都會完整記錄到 CSV 檔案中

### 5. 數據封包格式與解析

#### 標準數據封包結構（22 bytes）
```c
// 封包標頭
bleBuf[0] = 0xAA;           // 起始標記1
bleBuf[1] = 0xBB;           // 起始標記2
bleBuf[2] = sys_type;       // 系統類型標識
bleBuf[3] = batt_percentage; // 電池電量 (0-100%)

// 6軸原始數據 - 加速度 (int16, ±16g 對應 ±32768)
bleBuf[4]  = (ax >> 8) & 0xFF;  // 加速度X軸高位
bleBuf[5]  = ax & 0xFF;         // 加速度X軸低位
bleBuf[6]  = (ay >> 8) & 0xFF;  // 加速度Y軸高位
bleBuf[7]  = ay & 0xFF;         // 加速度Y軸低位
bleBuf[8]  = (az >> 8) & 0xFF;  // 加速度Z軸高位
bleBuf[9]  = az & 0xFF;         // 加速度Z軸低位

// 6軸原始數據 - 角速度 (int16, ±2000dps 對應 ±32768)
bleBuf[10] = (gx >> 8) & 0xFF;  // 角速度X軸高位
bleBuf[11] = gx & 0xFF;         // 角速度X軸低位
bleBuf[12] = (gy >> 8) & 0xFF;  // 角速度Y軸高位
bleBuf[13] = gy & 0xFF;         // 角速度Y軸低位
bleBuf[14] = (gz >> 8) & 0xFF;  // 角速度Z軸高位
bleBuf[15] = gz & 0xFF;         // 角速度Z軸低位

// Fusion數據 - 歐拉角 (int16, ±180° 對應 ±32768)
bleBuf[16] = (roll >> 8) & 0xFF;  // 橫滾角高位
bleBuf[17] = roll & 0xFF;         // 橫滾角低位
bleBuf[18] = (pitch >> 8) & 0xFF; // 俯仰角高位
bleBuf[19] = pitch & 0xFF;        // 俯仰角低位
bleBuf[20] = (yaw >> 8) & 0xFF;   // 偏航角高位
bleBuf[21] = yaw & 0xFF;          // 偏航角低位
```

#### 數據轉換公式
- **加速度**：`實際值(g) = 原始值 × 16.0 / 32768.0`
- **角速度**：`實際值(°/s) = 原始值 × 2000.0 / 32768.0`
- **歐拉角**：`實際值(°) = 原始值 × 180.0 / 32768.0`

#### 四元數計算
系統自動從歐拉角計算四元數：
```python
q0 = cos(roll/2) * cos(pitch/2) * cos(yaw/2) + sin(roll/2) * sin(pitch/2) * sin(yaw/2)
q1 = sin(roll/2) * cos(pitch/2) * cos(yaw/2) - cos(roll/2) * sin(pitch/2) * sin(yaw/2)
q2 = cos(roll/2) * sin(pitch/2) * cos(yaw/2) + sin(roll/2) * cos(pitch/2) * sin(yaw/2)
q3 = cos(roll/2) * cos(pitch/2) * sin(yaw/2) - sin(roll/2) * sin(pitch/2) * cos(yaw/2)
```

### 6. 即時資料顯示與繪圖
   - **2x2 卡片佈局**：主視窗以現代化卡片格式顯示 4 組感測器數據
   - **動態 MAC 選擇**：每個卡片可下拉選擇可用的 MAC 地址，支援空白選項（不記錄）
   - **即時曲線圖**：高性能的即時數據繪圖，採用簡化的單一Y軸設計
     - **統一Y軸**：所有數據都顯示在同一個圖表上，避免複雜的雙Y軸問題
     - **動態Y軸標籤**：根據當前模式自動調整Y軸標籤和單位
     - **自動範圍調整**：根據當前顯示的數據自動調整Y軸範圍
   - **靈活數據顯示**：可選擇顯示加速度、角速度、歐拉角等不同數據內容
   - **即時切換**：無需重新連接，可隨時在不同數據視圖間切換
   - **多軸同時顯示**：支援同時顯示 X、Y、Z 三軸數據
   - **智能模式切換**：
     - **加速度模式**：僅顯示 ax, ay, az 曲線，Y軸標籤為「Acceleration (g)」
     - **角速度模式**：僅顯示 gx, gy, gz 曲線，Y軸標籤為「Angular Velocity (°/s)」
     - **歐拉角模式**：僅顯示 roll, pitch, yaw 曲線，Y軸標籤為「Euler Angle (°)」
   - **簡化的可見性控制**：統一的線條顯示/隱藏控制，確保所有模式都能正確顯示
   - **FPS 監控**：即時顯示每個感測器的數據更新頻率
   - **電池監控**：即時顯示每個感測器的電池電量與狀態
   - **四元數顯示**：即時顯示計算出的四元數值

### 7. 運動指標計算系統
   - **最大瞬時速度**：基於慣性加速度積分計算的真實運動速度（去除重力影響）
   - **最大爆發力**：計算去除重力影響後的最大加速度模長
   - **最大角度變化**：相對於起始姿態的最大旋轉角度（使用四元數計算）
   - **自動重設功能**：偵測到所有感測器靜止 5 秒後自動重設指標
   - **手動重設**：支援一鍵手動重設所有運動指標
   - **詳細計算方法**：完整的數學公式與實作細節請參考 `MOTION_METRICS_CALCULATION.md`

### 8. 資料記錄與匯出系統
   - **獨立檔案管理**：每組感測器可分別設定檔案名稱與編號
   - **批次記錄控制**：支援一鍵開始/停止所有感測器的資料記錄
   - **完整數據記錄**：無論 UI 顯示什麼內容，都會記錄完整的感測器數據
   - **智能 CSV 格式**：根據用戶選擇的顯示模式調整 CSV 欄位結構

   **6軸原始數據 CSV 格式**：
   ```csv
   timestamp,imu_x,imu_y,imu_z,imu_a,imu_b,imu_c
   ```

   **歐拉角數據 CSV 格式**：
   ```csv
   timestamp,roll,pitch,yaw
   ```

   **完整數據 CSV 格式**（可選）：
   ```csv
   timestamp,ax,ay,az,gx,gy,gz,roll,pitch,yaw,q0,q1,q2,q3
   ```

   - **精確時間戳**：毫秒級時間戳，多筆資料自動平均分配時間間隔
   - **檔名規則**：`檔案名稱_MAC後四碼_編號.csv`
   - **緩衝寫入**：批次寫入機制，提升性能並減少磁碟 I/O
   - **數據完整性**：確保所有接收到的數據都能正確記錄

### 9. 數據轉發系統
   - **多協議支援**：UDP、TCP、WebSocket、MQTT
   - **即時轉發**：數據接收後立即轉發，延遲極低
   - **JSON 格式**：標準化的 JSON 數據格式，易於第三方集成
   - **分 MAC 轉發**：每個 MAC 地址的數據獨立發送
   - **錯誤處理**：完善的錯誤處理與狀態監控

### 10. 錯誤處理與用戶體驗
   - **智能錯誤提示**：連線、資料寫入等過程的詳細錯誤提示
   - **自動重連機制**：Serial 連線意外斷線時的自動處理
   - **狀態指示器**：即時顯示各組件的運行狀態
   - **現代化 UI**：採用現代化設計風格，提升用戶體驗

---

## 數據流架構

### 數據接收流程
```
BLE感測器 → Gateway → Serial Port → 主程式 → 數據解析 → 多路分發
                                              ↓
                                    ┌─────────┼─────────┐
                                    ↓         ↓         ↓
                              即時顯示    運動指標    數據轉發
                                    ↓         ↓         ↓
                              圖表更新    指標更新    外部系統
                                    ↓
                              CSV記錄
```

### 分析系統數據流
```
CSV檔案 → 數據載入 → 分析引擎 → 結果輸出
                      ↓
              ┌───────┼───────┐
              ↓       ↓       ↓
          歷史分析  比較分析  趨勢分析
              ↓       ↓       ↓
          指標計算  差異對比  趨勢預測
```

---

## 操作流程

### 基本操作流程
1. **啟動程式**：執行 `python main.py`，主畫面顯示現代化的頁籤式介面
2. **連線設定**：在實時監控頁籤中選擇 Serial COM Port 並連線
3. **感測器配置**：每個卡片下拉選擇欲顯示的感測器 MAC 地址
4. **即時監控**：連線成功後即時顯示各感測器資料曲線與運動指標
5. **數據顯示選擇**：可選擇顯示加速度、角速度、歐拉角等不同數據內容，或勾選/取消顯示特定資料曲線
6. **數據記錄**：設定檔案名稱與編號，點擊「Record」開始資料記錄
7. **數據轉發**：配置轉發協議與目標地址，啟動即時數據轉發
8. **停止記錄**：點擊「Stop Record」結束記錄，檔案自動儲存

### 進階分析流程
1. **數據分析**：切換至數據分析頁籤，載入歷史 CSV 檔案
2. **回放控制**：設定回放速度與時間範圍，進行數據回放分析
3. **比較分析**：在比較分析頁籤中載入多個檔案進行對比
4. **趨勢分析**：在趨勢分析頁籤中分析長期訓練趨勢
5. **報告生成**：導出分析報告與圖表

---

## 專案架構

### 核心模組
- **`main.py`**：主程式入口（建議執行檔）
- **`app_pyside6.py`**：主要 UI 控制邏輯與應用程式核心
- **`serial_manager.py`**：Serial Port 通訊與數據接收管理
- **`plotter.py`**：高性能即時數據繪圖元件
- **`motion_metrics.py`**：運動指標計算引擎
- **`data_forwarder.py`**：多協議數據轉發系統

### 分析模組
- **`data_analysis_tab.py`**：歷史數據分析頁籤
- **`comparison_tab.py`**：多檔案比較分析頁籤
- **`trend_analysis_tab.py`**：長期趨勢分析頁籤

### UI 組件
- **`modern_widgets.py`**：現代化 UI 組件庫
- **`modern_styles.py`**：統一的現代化樣式系統

### 測試工具
- **`test_data_receiver.py`**：數據轉發功能測試工具
- **`test_tabs_ui.py`**：UI 頁籤功能測試工具

### 技術文檔
- **`MOTION_METRICS_CALCULATION.md`**：運動指標計算方法的完整數學推導與實作細節
- **`DATA_FORWARDING_README.md`**：數據轉發系統的詳細使用說明

---

## 安裝與執行

### 基本環境需求
- **Python 3.8+**
- **作業系統**：Windows 10/11、macOS、Linux

### 1. 安裝核心依賴
```bash
pip install PySide6 matplotlib pyserial numpy scipy pyqtgraph
```

**重要提醒**：本專案完全使用 PySide6 作為 GUI 框架，不使用任何 PyQt 套件。pyqtgraph 會自動配置使用 PySide6 作為後端。

### 2. 安裝可選依賴（數據轉發功能）
```bash
# WebSocket 支援
pip install websocket-client

# MQTT 支援
pip install paho-mqtt
```

### 3. 執行程式
```bash
# 主程式（推薦）
python main.py
```

### 4. 測試工具
```bash
# 測試數據轉發功能
python test_data_receiver.py

# 測試 UI 頁籤功能
python test_tabs_ui.py
```

---

## 系統需求與注意事項

### 硬體需求
- **Gateway 設備**：已正確連接電腦並開啟
- **IMU 感測器**：已開機並與 Gateway 配對
- **Serial Port**：確保有足夠的 COM Port 權限

### 軟體需求
- **Serial Port 驅動**：確保 Gateway 驅動程式已正確安裝
- **防火牆設定**：數據轉發功能需要網路權限
- **磁碟空間**：長時間記錄需要足夠的儲存空間

### 故障排除
- **無法掃描到裝置**：檢查 Gateway 狀態與 Serial Port 權限
- **數據轉發失敗**：檢查網路連接與目標系統狀態
- **記錄檔案錯誤**：確保執行目錄有寫入權限
- **UI 顯示異常**：確認 PySide6 版本兼容性

### 檔案輸出格式
- **記錄檔案**：`<自訂名稱>_<MAC後四碼>_<編號>.csv`
- **儲存位置**：程式執行目錄下
- **分析報告**：支援 PDF、PNG、CSV 等格式導出

---

## 技術支援

如有技術問題或功能建議，請聯絡專案維護者。

---

## 技術細節與實作說明

### 數據解析機制
- **純文字解析**：僅需解析 `NUS_RX:<MAC>,<DATA>;` 格式
- **HEX 字串處理**：`<DATA>` 為空格分隔的 HEX 字串
- **多筆資料支援**：一筆 NUS_RX 可能包含多筆感測器資料
- **自動分包處理**：若 NUS_RX 被分包，buffer 自動累積直到收到完整的 `;`

### 統一數據處理
- **統一封包格式**：所有數據都包含在同一個 22-byte 封包中
- **無需模式識別**：不再需要根據 sys_type 進行模式判斷
- **完整數據可用**：6軸原始數據、歐拉角、四元數同時可用
- **用戶選擇顯示**：由用戶在 UI 中選擇要顯示的數據類型
- **向下兼容**：保持與舊版本數據格式的兼容性

### MAC 地址管理
- **動態偵測**：直接從 Serial 資料內容取得當前存在的感測器 MAC 地址
- **即時更新**：新感測器會直接收到數據並出現在下拉選單
- **無需預設列表**：不需要預先配置 MAC 地址列表

### 時間戳處理
- **毫秒精度**：timestamp 欄位為毫秒間隔
- **智能分配**：多筆資料自動平均分配時間間隔
- **即時更新**：圖表與資料記錄根據串流內容即時動態更新

### Serial Port 管理
- **自動掃描**：僅顯示本機可用的 Serial Port
- **連線控制**：提供連線與斷線按鈕
- **異常處理**：能處理意外斷線並提供重連機制

### UI 顯示優化
- **單位標示**：單位顯示於圖表座標軸旁
- **即時更新**：所有顯示元件根據數據流即時更新
- **性能優化**：採用高效的繪圖與數據處理機制

---

## 進階功能與未來發展

### 🔬 數據分析系統（部分實現）

#### 歷史數據分析
- **CSV 檔案回放**：載入歷史記錄進行詳細分析
- **時間範圍選擇**：精確控制分析的時間段
- **運動指標重算**：重新計算歷史數據的運動指標
- **回放控制**：支援不同速度的數據回放

#### 比較分析功能
- **多檔案對比**：同時比較多個訓練記錄
- **指標差異分析**：詳細的數值比較與百分比差異
- **視覺化對比**：圖表形式的直觀比較
- **比較模式**：整體比較、時間段比較、峰值比較

#### 趨勢分析系統
- **長期追蹤**：分析訓練進度與改善趨勢
- **統計摘要**：自動生成訓練統計報告
- **趨勢預測**：基於歷史數據的趨勢預測
- **智能洞察**：提供數據驅動的訓練建議

### 🤖 AI/ML 康復訓練應用（規劃中）

#### 1. 智能動作識別
- **深度學習模型**：CNN、LSTM、Bi-LSTM、GRU 架構
- **動作分類**：站立、蹲下、抬臂、走路等康復動作識別
- **自動分段**：解析動作步驟（彎腰→抬頭→抬臂）的時序與品質
- **即時識別**：邊緣推論延遲 < 100ms

#### 2. 動作品質評估
- **多維度評估**：速度、範圍、穩定性、流暢度
- **偏差偵測**：幅度不足、速度異常、姿態歪斜
- **品質評分**：結合深度模型與康復評估指標
- **異常警報**：即時偵測危險動作或錯誤姿態

#### 3. 智能訓練管理
- **動作量化**：自動計數「功能動作原子」
- **劑量建議**：基於個人能力的訓練強度建議
- **進度追蹤**：每日訓練次數、時長、強度統計
- **個人化計劃**：根據康復進度調整訓練計劃

#### 4. LLM 整合與智能回饋
- **即時指導**：語音或 UI 提供即時動作指導
- **自然語言回饋**：結合 GPT、Gemini 生成個人化建議
- **語意化分析**：將數據分析結果轉換為易懂的語言
- **互動式對話**：支援用戶與系統的自然語言交互

#### 5. 數據科學平台
- **標註工具**：提供動作標記與品質評分工具
- **模型訓練**：支援用戶自定義模型訓練流程
- **實驗平台**：ML/DL 算法實驗與比較平台
- **API 接口**：提供標準化的數據與模型 API

### 🏥 臨床應用場景

#### 康復醫學
- **物理治療**：上肢、下肢功能康復訓練
- **神經康復**：中風、帕金森病患者動作訓練
- **骨科康復**：術後功能恢復訓練
- **老年醫學**：平衡能力與跌倒預防訓練

#### 運動科學
- **運動表現分析**：專業運動員技術分析
- **傷害預防**：運動傷害風險評估
- **訓練優化**：個人化訓練計劃制定
- **生物力學研究**：人體運動機制研究

#### 遠程醫療
- **居家康復**：遠程監控與指導
- **數據共享**：醫患之間的數據同步
- **進度報告**：自動生成康復進度報告
- **專家會診**：支援多專家遠程會診

---