#!/usr/bin/env python3
"""
簡單的數據接收測試腳本
用於測試IMU數據轉發功能
"""

import socket
import json
import threading
import time

class UDPReceiver:
    def __init__(self, host='127.0.0.1', port=8888):
        self.host = host
        self.port = port
        self.socket = None
        self.running = False
        self.thread = None
        
    def start(self):
        """啟動UDP接收器"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.bind((self.host, self.port))
            self.running = True
            self.thread = threading.Thread(target=self._receive_loop, daemon=True)
            self.thread.start()
            print(f"UDP接收器已啟動，監聽 {self.host}:{self.port}")
            return True
        except Exception as e:
            print(f"啟動UDP接收器失敗: {e}")
            return False
    
    def stop(self):
        """停止UDP接收器"""
        self.running = False
        if self.socket:
            self.socket.close()
            
    def _receive_loop(self):
        """接收循環"""
        while self.running:
            try:
                data, addr = self.socket.recvfrom(4096)
                message = data.decode('utf-8')
                self._process_message(message, addr)
            except Exception as e:
                if self.running:
                    print(f"接收數據時出錯: {e}")
                break
    
    def _process_message(self, message, addr):
        """處理接收到的消息"""
        try:
            data = json.loads(message)
            timestamp = data.get('timestamp', 0)
            mac = data.get('mac', 'Unknown')
            imu_data = data.get('data', {})
            battery = data.get('battery', 0)
            
            # 提取關鍵數據
            ax = imu_data.get('ax', 0)
            ay = imu_data.get('ay', 0) 
            az = imu_data.get('az', 0)
            roll = imu_data.get('roll', 0)
            pitch = imu_data.get('pitch', 0)
            yaw = imu_data.get('yaw', 0)
            
            print(f"[{time.strftime('%H:%M:%S')}] MAC: {mac} | "
                  f"加速度: ({ax:.3f}, {ay:.3f}, {az:.3f})g | "
                  f"姿態: R{roll:.1f}° P{pitch:.1f}° Y{yaw:.1f}° | "
                  f"電量: {battery}%")
                  
        except json.JSONDecodeError:
            print(f"無法解析JSON數據: {message[:100]}...")
        except Exception as e:
            print(f"處理消息時出錯: {e}")

class TCPReceiver:
    def __init__(self, host='127.0.0.1', port=8888):
        self.host = host
        self.port = port
        self.socket = None
        self.running = False
        self.thread = None
        
    def start(self):
        """啟動TCP接收器"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.bind((self.host, self.port))
            self.socket.listen(5)
            self.running = True
            self.thread = threading.Thread(target=self._accept_loop, daemon=True)
            self.thread.start()
            print(f"TCP接收器已啟動，監聽 {self.host}:{self.port}")
            return True
        except Exception as e:
            print(f"啟動TCP接收器失敗: {e}")
            return False
    
    def stop(self):
        """停止TCP接收器"""
        self.running = False
        if self.socket:
            self.socket.close()
            
    def _accept_loop(self):
        """接受連接循環"""
        while self.running:
            try:
                client_socket, addr = self.socket.accept()
                print(f"客戶端已連接: {addr}")
                client_thread = threading.Thread(
                    target=self._handle_client, 
                    args=(client_socket, addr), 
                    daemon=True
                )
                client_thread.start()
            except Exception as e:
                if self.running:
                    print(f"接受連接時出錯: {e}")
                break
    
    def _handle_client(self, client_socket, addr):
        """處理客戶端連接"""
        buffer = ""
        try:
            while self.running:
                data = client_socket.recv(4096)
                if not data:
                    break
                    
                buffer += data.decode('utf-8')
                while '\n' in buffer:
                    line, buffer = buffer.split('\n', 1)
                    if line.strip():
                        self._process_message(line.strip(), addr)
                        
        except Exception as e:
            print(f"處理客戶端 {addr} 時出錯: {e}")
        finally:
            client_socket.close()
            print(f"客戶端 {addr} 已斷開")
    
    def _process_message(self, message, addr):
        """處理接收到的消息"""
        try:
            data = json.loads(message)
            timestamp = data.get('timestamp', 0)
            mac = data.get('mac', 'Unknown')
            imu_data = data.get('data', {})
            battery = data.get('battery', 0)
            
            # 提取關鍵數據
            ax = imu_data.get('ax', 0)
            ay = imu_data.get('ay', 0) 
            az = imu_data.get('az', 0)
            roll = imu_data.get('roll', 0)
            pitch = imu_data.get('pitch', 0)
            yaw = imu_data.get('yaw', 0)
            
            print(f"[{time.strftime('%H:%M:%S')}] MAC: {mac} | "
                  f"加速度: ({ax:.3f}, {ay:.3f}, {az:.3f})g | "
                  f"姿態: R{roll:.1f}° P{pitch:.1f}° Y{yaw:.1f}° | "
                  f"電量: {battery}%")
                  
        except json.JSONDecodeError:
            print(f"無法解析JSON數據: {message[:100]}...")
        except Exception as e:
            print(f"處理消息時出錯: {e}")

def main():
    print("IMU數據接收測試工具")
    print("1. UDP接收器")
    print("2. TCP接收器")
    
    choice = input("請選擇接收器類型 (1/2): ").strip()
    
    if choice == '1':
        receiver = UDPReceiver()
    elif choice == '2':
        receiver = TCPReceiver()
    else:
        print("無效選擇")
        return
    
    if receiver.start():
        try:
            print("接收器正在運行，按 Ctrl+C 停止...")
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n正在停止接收器...")
            receiver.stop()
            print("接收器已停止")
    else:
        print("無法啟動接收器")

if __name__ == '__main__':
    main()
