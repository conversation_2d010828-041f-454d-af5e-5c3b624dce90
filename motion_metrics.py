import numpy as np
from scipy.spatial.transform import Rotation as R
from collections import deque

class MotionMetrics:
    def __init__(self, window_sec=0.2):
        self.window_sec = window_sec
        self.reset()

    def reset(self):
        # 最大瞬時速度相關
        self.speed_buffer = deque()
        self.max_instant_speed = 0.0
        self.last_time = None
        self.last_velocity = np.zeros(3)
        # 最大爆發力
        self.max_explosive_acc = 0.0 # m/s^2
        # 最大角度
        self.start_quat_wxyz = None
        self.max_angle = 0.0

    def update(self, acc_g, quat_wxyz, dt):
        # acc_g: np.array([ax, ay, az]), 單位 g
        # quat_wxyz: np.array([w, x, y, z])
        # dt: 單位秒
        
        try:
            # --- 1. 計算慣性加速度 (Inertial Acceleration) ---
            # 創建從感測器座標系到世界座標系的旋轉
            r = R.from_quat([quat_wxyz[1], quat_wxyz[2], quat_wxyz[3], quat_wxyz[0]]) # [x, y, z, w]
            
            # 將感測器讀數旋轉到世界座標系
            # 靜止時 acc_g 約為 [0,0,1g] (Z軸朝上), 旋轉後 acc_g_world 約為 [0,0,1g]
            acc_g_world = r.apply(acc_g)
            
            # 從世界座標系的讀數中，減去靜態時抵銷重力的支持力分量 [0,0,1g]
            # 如此即可得到真實的慣性加速度
            # - 靜止時: [0,0,1] - [0,0,1] = 0 (慣性加速度為0)
            # - 自由落體時: [0,0,0] - [0,0,1] = -1g (慣性加速度為-1g)
            inertial_acc_world_g = acc_g_world - np.array([0, 0, 1])
            
            # 轉換單位為 m/s^2
            inertial_acc_world_ms2 = inertial_acc_world_g * 9.81

        except Exception as e:
            inertial_acc_world_ms2 = np.zeros(3)

        # --- 2. 最大爆發力（使用慣性加速度的模長）---
        acc_norm = np.linalg.norm(inertial_acc_world_ms2)
        if acc_norm > self.max_explosive_acc:
            self.max_explosive_acc = acc_norm

        # --- 3. 最大瞬時速度計算 (使用慣性加速度積分) ---
        if self.last_time is None:
            self.last_time = 0.0
        v_new = self.last_velocity + inertial_acc_world_ms2 * dt
        now = self.last_time + dt
        self.speed_buffer.append((now, v_new.copy()))
        self.last_velocity = v_new.copy()
        self.last_time = now
        while self.speed_buffer and (now - self.speed_buffer[0][0] > self.window_sec):
            self.speed_buffer.popleft()
        if len(self.speed_buffer) >= 2:
            v0 = self.speed_buffer[0][1]
            vmax = max(np.linalg.norm(v - v0) for _, v in self.speed_buffer)
            if vmax > self.max_instant_speed:
                self.max_instant_speed = vmax
            
        # --- 4. 最大角度計算（起始 vs 當前）---
        if self.start_quat_wxyz is None:
            self.start_quat_wxyz = quat_wxyz.copy()
        
        q_start_ref = self.start_quat_wxyz.copy()
        quat_current_ref = quat_wxyz.copy()

        if np.dot(q_start_ref, quat_current_ref) < 0:
            quat_current_ref = -quat_current_ref

        try:
            r_start = R.from_quat([q_start_ref[1], q_start_ref[2], q_start_ref[3], q_start_ref[0]])
            r_current = R.from_quat([quat_current_ref[1], quat_current_ref[2], quat_current_ref[3], quat_current_ref[0]])
            r_diff = r_start.inv() * r_current
            angle_mag = np.linalg.norm(r_diff.as_rotvec(degrees=True))

            if angle_mag > self.max_angle:
                self.max_angle = angle_mag
        except Exception as e:
            pass

    def get_max_instant_speed(self):
        return self.max_instant_speed

    def get_max_explosive_acc(self):
        return self.max_explosive_acc

    def get_max_angle(self):
        return self.max_angle 