# modern_styles.py
# 現代化UI樣式定義

class ModernStyles:
    """現代化UI樣式類，提供科技感的深色主題樣式"""
    
    # 配色方案
    COLORS = {
        'primary': '#1e1e2e',           # 主背景色
        'secondary': '#2a2a3e',         # 次要背景色
        'accent': '#00d4ff',            # 強調色（青藍色）
        'accent_hover': '#00b8e6',      # 強調色懸停
        'success': '#00ff88',           # 成功色（綠色）
        'warning': '#ffaa00',           # 警告色（橙色）
        'danger': '#ff4757',            # 危險色（紅色）
        'text_primary': '#ffffff',      # 主要文字色
        'text_secondary': '#b8b8cc',    # 次要文字色
        'border': '#3a3a4e',            # 邊框色
        'card_bg': '#252540',           # 卡片背景色
        'sidebar_bg': '#1a1a2e',        # 側邊欄背景色
        'gradient_start': '#667eea',    # 漸變起始色
        'gradient_end': '#764ba2',      # 漸變結束色
    }
    
    @staticmethod
    def get_main_window_style():
        """主窗口樣式"""
        return f"""
        QMainWindow {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {ModernStyles.COLORS['primary']},
                stop:1 {ModernStyles.COLORS['secondary']});
            color: {ModernStyles.COLORS['text_primary']};
            font-family: 'Segoe UI', Arial, sans-serif;
        }}
        """
    
    @staticmethod
    def get_sidebar_style():
        """側邊欄樣式"""
        return f"""
        QWidget#sidebar {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {ModernStyles.COLORS['sidebar_bg']},
                stop:1 {ModernStyles.COLORS['primary']});
            border-right: 2px solid {ModernStyles.COLORS['border']};
            min-width: 220px;
            max-width: 220px;
        }}
        
        QLabel#sidebar_title {{
            color: {ModernStyles.COLORS['accent']};
            font-size: 18px;
            font-weight: bold;
            padding: 15px;
            border-bottom: 1px solid {ModernStyles.COLORS['border']};
        }}
        """
    
    @staticmethod
    def get_button_style():
        """按鈕樣式"""
        return f"""
        QPushButton {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {ModernStyles.COLORS['accent']},
                stop:1 {ModernStyles.COLORS['accent_hover']});
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-size: 12px;
            font-weight: bold;
            min-height: 35px;
        }}
        
        QPushButton:hover {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {ModernStyles.COLORS['accent_hover']},
                stop:1 {ModernStyles.COLORS['accent']});
            transform: translateY(-1px);
        }}
        
        QPushButton:pressed {{
            background: {ModernStyles.COLORS['accent_hover']};
            transform: translateY(1px);
        }}
        
        QPushButton:disabled {{
            background: {ModernStyles.COLORS['border']};
            color: {ModernStyles.COLORS['text_secondary']};
        }}
        
        QPushButton#danger_btn {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {ModernStyles.COLORS['danger']},
                stop:1 #e74c3c);
        }}
        
        QPushButton#success_btn {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {ModernStyles.COLORS['success']},
                stop:1 #27ae60);
        }}
        """
    
    @staticmethod
    def get_card_style():
        """卡片樣式"""
        return f"""
        QWidget#sensor_card {{
            background: {ModernStyles.COLORS['card_bg']};
            border: 1px solid {ModernStyles.COLORS['border']};
            border-radius: 12px;
            margin: 8px;
        }}
        
        QWidget#sensor_card:hover {{
            border: 1px solid {ModernStyles.COLORS['accent']};
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
        }}
        
        QLabel#card_title {{
            color: {ModernStyles.COLORS['accent']};
            font-size: 15px;
            font-weight: bold;
            padding: 10px;
            border-bottom: 1px solid {ModernStyles.COLORS['border']};
        }}
        """
    
    @staticmethod
    def get_combo_style():
        """下拉選單樣式"""
        return f"""
        QComboBox {{
            background: {ModernStyles.COLORS['secondary']};
            color: {ModernStyles.COLORS['text_primary']};
            border: 1px solid {ModernStyles.COLORS['border']};
            border-radius: 6px;
            padding: 8px 12px;
            min-height: 25px;
        }}
        
        QComboBox:hover {{
            border: 1px solid {ModernStyles.COLORS['accent']};
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 20px;
        }}
        
        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {ModernStyles.COLORS['text_secondary']};
        }}
        
        QComboBox QAbstractItemView {{
            background: {ModernStyles.COLORS['secondary']};
            color: {ModernStyles.COLORS['text_primary']};
            border: 1px solid {ModernStyles.COLORS['border']};
            border-radius: 6px;
            selection-background-color: {ModernStyles.COLORS['accent']};
        }}
        """
    
    @staticmethod
    def get_label_style():
        """標籤樣式"""
        return f"""
        QLabel {{
            color: {ModernStyles.COLORS['text_primary']};
            font-size: 13px;
        }}
        
        QLabel#metric_label {{
            color: {ModernStyles.COLORS['text_secondary']};
            font-size: 11px;
            padding: 4px 8px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
            margin: 2px;
        }}
        
        QLabel#metric_value {{
            color: {ModernStyles.COLORS['accent']};
            font-size: 14px;
            font-weight: bold;
            padding: 4px 8px;
        }}
        
        QLabel#status_connected {{
            color: {ModernStyles.COLORS['success']};
            font-weight: bold;
            font-size: 13px;
        }}

        QLabel#status_disconnected {{
            color: {ModernStyles.COLORS['danger']};
            font-weight: bold;
            font-size: 13px;
        }}
        """
    
    @staticmethod
    def get_input_style():
        """輸入框樣式"""
        return f"""
        QLineEdit {{
            background: {ModernStyles.COLORS['secondary']};
            color: {ModernStyles.COLORS['text_primary']};
            border: 1px solid {ModernStyles.COLORS['border']};
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 12px;
        }}
        
        QLineEdit:focus {{
            border: 2px solid {ModernStyles.COLORS['accent']};
            background: {ModernStyles.COLORS['card_bg']};
        }}
        """
    
    @staticmethod
    def get_tab_style():
        """標籤頁樣式"""
        return f"""
        QTabWidget::pane {{
            border: 1px solid {ModernStyles.COLORS['border']};
            border-radius: 8px;
            background: {ModernStyles.COLORS['card_bg']};
        }}
        
        QTabBar::tab {{
            background: {ModernStyles.COLORS['secondary']};
            color: {ModernStyles.COLORS['text_secondary']};
            padding: 12px 20px;
            margin-right: 2px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }}
        
        QTabBar::tab:selected {{
            background: {ModernStyles.COLORS['accent']};
            color: white;
        }}
        
        QTabBar::tab:hover:!selected {{
            background: {ModernStyles.COLORS['border']};
            color: {ModernStyles.COLORS['text_primary']};
        }}
        """
    
    @staticmethod
    def get_complete_style():
        """獲取完整樣式"""
        return (
            ModernStyles.get_main_window_style() +
            ModernStyles.get_sidebar_style() +
            ModernStyles.get_button_style() +
            ModernStyles.get_card_style() +
            ModernStyles.get_combo_style() +
            ModernStyles.get_label_style() +
            ModernStyles.get_input_style() +
            ModernStyles.get_tab_style()
        )
