# data_analysis_tab.py
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QFileDialog, QGroupBox, QGridLayout, QProgressBar, QTextEdit,
    QComboBox, QSlider, QSpinBox
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont
from modern_widgets import ModernButton, SidebarSection

class DataAnalysisTab(QWidget):
    """數據分析頁籤"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self._setup_ui()
        # 添加示例日誌
        self._add_sample_logs()
    
    def _setup_ui(self):
        """設置UI佈局"""
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(15)
        
        # 左側控制面板
        control_panel = self._create_control_panel()
        main_layout.addWidget(control_panel, 1)
        
        # 右側顯示區域
        display_area = self._create_display_area()
        main_layout.addWidget(display_area, 3)
    
    def _create_control_panel(self):
        """創建左側控制面板"""
        panel = QWidget()
        panel.setMaximumWidth(300)
        layout = QVBoxLayout(panel)
        layout.setSpacing(15)
        
        # 檔案載入區域
        file_section = SidebarSection("檔案載入")
        file_layout = QVBoxLayout()
        
        self.file_label = QLabel("未選擇檔案")
        self.file_label.setWordWrap(True)
        self.file_label.setStyleSheet("color: #666; padding: 8px; background: rgba(255,255,255,0.05); border-radius: 4px;")
        file_layout.addWidget(self.file_label)
        
        self.load_file_btn = ModernButton("選擇CSV檔案", "primary")
        file_layout.addWidget(self.load_file_btn)
        
        file_section.setLayout(file_layout)
        layout.addWidget(file_section)
        
        # 回放控制區域
        playback_section = SidebarSection("回放控制")
        playback_layout = QVBoxLayout()
        
        # 回放按鈕
        button_layout = QHBoxLayout()
        self.play_btn = ModernButton("▶️", "success")
        self.play_btn.setMaximumWidth(50)
        self.pause_btn = ModernButton("⏸️", "warning")
        self.pause_btn.setMaximumWidth(50)
        self.stop_btn = ModernButton("⏹️", "danger")
        self.stop_btn.setMaximumWidth(50)
        
        button_layout.addWidget(self.play_btn)
        button_layout.addWidget(self.pause_btn)
        button_layout.addWidget(self.stop_btn)
        button_layout.addStretch()
        playback_layout.addLayout(button_layout)
        
        # 進度條
        self.progress_slider = QSlider(Qt.Orientation.Horizontal)
        self.progress_slider.setMinimum(0)
        self.progress_slider.setMaximum(100)
        self.progress_slider.setValue(0)
        playback_layout.addWidget(QLabel("進度:"))
        playback_layout.addWidget(self.progress_slider)
        
        # 播放速度
        speed_layout = QHBoxLayout()
        speed_layout.addWidget(QLabel("速度:"))
        self.speed_combo = QComboBox()
        self.speed_combo.addItems(["0.5x", "1x", "1.5x", "2x", "3x"])
        self.speed_combo.setCurrentText("1x")
        speed_layout.addWidget(self.speed_combo)
        playback_layout.addLayout(speed_layout)
        
        playback_section.setLayout(playback_layout)
        layout.addWidget(playback_section)
        
        # 分析設置區域
        analysis_section = SidebarSection("分析設置")
        analysis_layout = QVBoxLayout()
        
        # 時間範圍選擇
        time_layout = QVBoxLayout()
        time_layout.addWidget(QLabel("分析時間範圍:"))
        
        start_layout = QHBoxLayout()
        start_layout.addWidget(QLabel("開始:"))
        self.start_time_spin = QSpinBox()
        self.start_time_spin.setSuffix(" 秒")
        self.start_time_spin.setMinimum(0)
        self.start_time_spin.setMaximum(3600)
        start_layout.addWidget(self.start_time_spin)
        time_layout.addLayout(start_layout)
        
        end_layout = QHBoxLayout()
        end_layout.addWidget(QLabel("結束:"))
        self.end_time_spin = QSpinBox()
        self.end_time_spin.setSuffix(" 秒")
        self.end_time_spin.setMinimum(0)
        self.end_time_spin.setMaximum(3600)
        self.end_time_spin.setValue(60)
        end_layout.addWidget(self.end_time_spin)
        time_layout.addLayout(end_layout)
        
        analysis_layout.addLayout(time_layout)
        
        # 分析按鈕
        self.analyze_btn = ModernButton("開始分析", "primary")
        analysis_layout.addWidget(self.analyze_btn)
        
        analysis_section.setLayout(analysis_layout)
        layout.addWidget(analysis_section)
        
        layout.addStretch()
        return panel
    
    def _create_display_area(self):
        """創建右側顯示區域"""
        display = QWidget()
        layout = QVBoxLayout(display)
        layout.setSpacing(15)
        
        # 標題
        title = QLabel("數據分析結果")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setStyleSheet("color: #00d4ff; padding: 10px;")
        layout.addWidget(title)
        
        # 運動指標顯示區域
        metrics_group = QGroupBox("運動指標")
        metrics_layout = QGridLayout(metrics_group)
        
        # 創建指標顯示卡片
        self.speed_card = self._create_metric_card("最大速度", "-- m/s", "#00aa00")
        self.acc_card = self._create_metric_card("最大加速度", "-- m/s²", "#0055aa")
        self.force_card = self._create_metric_card("最大力度", "-- g", "#aa0055")
        
        metrics_layout.addWidget(self.speed_card, 0, 0)
        metrics_layout.addWidget(self.acc_card, 0, 1)
        metrics_layout.addWidget(self.force_card, 0, 2)
        
        layout.addWidget(metrics_group)
        
        # 圖表顯示區域（預留）
        chart_group = QGroupBox("數據圖表")
        chart_layout = QVBoxLayout(chart_group)
        
        self.chart_placeholder = QLabel("圖表將在此顯示")
        self.chart_placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.chart_placeholder.setStyleSheet("""
            background: rgba(255, 255, 255, 0.05);
            border: 2px dashed #666;
            border-radius: 8px;
            padding: 40px;
            color: #666;
            font-size: 14px;
        """)
        self.chart_placeholder.setMinimumHeight(300)
        chart_layout.addWidget(self.chart_placeholder)
        
        layout.addWidget(chart_group)
        
        # 分析日誌區域
        log_group = QGroupBox("分析日誌")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(120)
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            background: rgba(0, 0, 0, 0.3);
            color: #ccc;
            border: 1px solid #444;
            border-radius: 4px;
            font-family: 'Consolas', monospace;
            font-size: 11px;
        """)
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        return display
    
    def _create_metric_card(self, title, value, color):
        """創建運動指標卡片"""
        card = QWidget()
        card.setStyleSheet(f"""
            QWidget {{
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid #3a3a4e;
                border-radius: 8px;
                padding: 15px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setSpacing(8)
        
        # 標題
        title_label = QLabel(title)
        title_label.setStyleSheet(f"color: {color}; font-weight: bold; font-size: 12px;")
        layout.addWidget(title_label)
        
        # 數值
        value_label = QLabel(value)
        value_label.setStyleSheet(f"color: {color}; font-size: 18px; font-weight: bold;")
        value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(value_label)
        
        # 進度條
        progress = QProgressBar()
        progress.setMaximum(100)
        progress.setValue(0)
        progress.setTextVisible(False)
        progress.setFixedHeight(6)
        progress.setStyleSheet(f"""
            QProgressBar {{
                border: none;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 3px;
            }}
            QProgressBar::chunk {{
                background: {color};
                border-radius: 3px;
            }}
        """)
        layout.addWidget(progress)
        
        # 保存引用以便後續更新
        setattr(card, 'value_label', value_label)
        setattr(card, 'progress_bar', progress)
        
        return card
    
    def log_message(self, message):
        """添加日誌訊息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")

    def _add_sample_logs(self):
        """添加示例日誌"""
        self.log_message("系統初始化完成")
        self.log_message("等待用戶選擇數據檔案...")
        self.log_message("提示：支援CSV格式的IMU數據檔案")
