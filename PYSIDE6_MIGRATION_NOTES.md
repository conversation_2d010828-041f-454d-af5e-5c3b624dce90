# PySide6 遷移說明

## 概述

本專案已完全遷移至使用 PySide6 作為 GUI 框架，移除了所有 PyQt 相關的依賴和引用。

## 修改內容

### 1. 主程式修改 (main.py)
- 在程式開始時設置環境變數 `QT_API=pyside6`，強制 pyqtgraph 使用 PySide6 後端
- 確保在任何 Qt 相關模組導入之前就完成配置

### 2. 繪圖模組修改 (plotter.py)
- 移除 `from pyqtgraph.Qt import QtWidgets` 的導入
- 改為直接從 PySide6.QtWidgets 導入所需的組件：
  - QWidget
  - QVBoxLayout
  - QHBoxLayout
  - QRadioButton
  - QButtonGroup
  - QCheckBox
  - QLabel
  - QDialog

#### 2.1 繪圖系統重構 - 移除雙Y軸設計 (2025-08-06)
- **問題**：雙Y軸設計過於複雜，導致數據曲線顯示問題，只有歐拉角模式能看到數據曲線
- **解決方案**：完全重構繪圖系統，採用單一Y軸設計
- **主要改動**：
  - **移除雙Y軸**：移除 `right_axis` 和 `right_lines`，所有數據都使用同一個 plot widget
  - **簡化線條管理**：只使用 `self.lines` 字典管理所有線條
  - **動態Y軸標籤**：根據當前模式（加速度/角速度/歐拉角）動態更新Y軸標籤和單位
  - **統一可見性控制**：簡化可見性控制邏輯，所有線條使用相同的控制方式
  - **模式感知**：添加 `current_mode` 屬性追蹤當前顯示模式
- **新功能**：
  - `_update_y_axis_label()` - 根據模式更新Y軸標籤
  - 簡化的 `update_plot()` - 單一Y軸範圍計算
  - 優化的 `_on_mode_radio_changed()` - 同時更新模式和Y軸標籤
- **影響**：現在所有模式的數據曲線都能正確顯示，數據不會超出圖表邊界
- 替換所有 `QtWidgets.XXX()` 為對應的 PySide6 組件

### 3. PyInstaller 配置修改 (main.spec)
- 在 excludes 列表中添加 PyQt5、PyQt6、PyQt4，防止意外打包

### 4. 文檔更新 (README.md)
- 在安裝說明中添加重要提醒，說明專案完全使用 PySide6
- 強調 pyqtgraph 會自動配置使用 PySide6 作為後端

### 5. 清理工作
- 刪除舊的 build 和 dist 目錄，移除包含 PyQt 引用的編譯檔案
- 確保所有 Python 檔案都只使用 PySide6

## 技術細節

### pyqtgraph 後端配置
pyqtgraph 是一個支援多種 Qt 後端的繪圖庫，包括：
- PyQt4
- PyQt5
- PyQt6
- PySide2
- PySide6

通過在程式開始時設置 `os.environ['QT_API'] = 'pyside6'`，可以強制 pyqtgraph 使用 PySide6 作為後端，避免自動選擇其他 Qt 實現。

### 相容性確保
所有 UI 組件都已確認使用 PySide6：
- ✅ main.py
- ✅ app_pyside6.py
- ✅ plotter.py
- ✅ modern_widgets.py
- ✅ modern_styles.py
- ✅ data_analysis_tab.py
- ✅ comparison_tab.py
- ✅ trend_analysis_tab.py
- ✅ test_tabs_ui.py
- ✅ data_forwarder.py (僅使用 PySide6.QtCore)

### 驗證方法
可以通過以下方式驗證專案是否正確使用 PySide6：

1. 檢查程式啟動時的環境變數設置
2. 確認所有 import 語句都來自 PySide6
3. 運行程式時檢查是否有 PyQt 相關的錯誤或警告

## 注意事項

1. **環境變數設置**：必須在任何 Qt 相關模組導入之前設置 `QT_API=pyside6`
2. **依賴安裝**：確保安裝了 PySide6 而不是 PyQt
3. **編譯打包**：重新編譯時會自動排除 PyQt 相關模組
4. **向後相容**：此修改不影響程式的功能，只是更換了底層的 Qt 實現

## 未來維護

在添加新的 UI 組件時，請確保：
1. 只從 PySide6 導入 Qt 相關模組
2. 不要使用 pyqtgraph.Qt 的組件
3. 保持環境變數設置在程式開始處
