import json
import socket
import threading
import time
import queue
from typing import Dict, Any, Optional
from PySide6.QtCore import QObject, Signal

# 可選依賴項
try:
    import websocket
    WEBSOCKET_AVAILABLE = True
except ImportError:
    WEBSOCKET_AVAILABLE = False

try:
    import paho.mqtt.client as mqtt
    MQTT_AVAILABLE = True
except ImportError:
    MQTT_AVAILABLE = False

class DataForwarder(QObject):
    """數據轉發管理器，支持多種協議將IMU數據轉發到外部系統"""
    
    status_changed = Signal(str, str)  # protocol, status
    error_occurred = Signal(str)  # error message
    
    def __init__(self):
        super().__init__()
        self.enabled = False
        self.protocol = "UDP"  # UDP, TCP, WebSocket, MQTT
        self.host = "127.0.0.1"
        self.port = 8888
        self.mqtt_topic = "imu/data"
        
        # 連接對象
        self.udp_socket = None
        self.tcp_socket = None
        self.websocket = None
        self.mqtt_client = None
        
        # 數據隊列和發送線程
        self.data_queue = queue.Queue(maxsize=1000)
        self.send_thread = None
        self.running = False
        
        # 統計信息
        self.sent_count = 0
        self.error_count = 0
        
    def configure(self, protocol: str, host: str, port: int, mqtt_topic: str = "imu/data"):
        """配置轉發參數"""
        self.protocol = protocol
        self.host = host
        self.port = port
        self.mqtt_topic = mqtt_topic
        
    def start_forwarding(self) -> bool:
        """開始數據轉發"""
        if self.enabled:
            return True
            
        try:
            if self.protocol == "UDP":
                self._start_udp()
            elif self.protocol == "TCP":
                self._start_tcp()
            elif self.protocol == "WebSocket":
                self._start_websocket()
            elif self.protocol == "MQTT":
                self._start_mqtt()
            else:
                raise ValueError(f"不支持的協議: {self.protocol}")
                
            self.enabled = True
            self.running = True
            self.send_thread = threading.Thread(target=self._send_loop, daemon=True)
            self.send_thread.start()
            
            self.status_changed.emit(self.protocol, "已連接")
            return True
            
        except Exception as e:
            self.error_occurred.emit(f"啟動轉發失敗: {str(e)}")
            return False
    
    def stop_forwarding(self):
        """停止數據轉發"""
        if not self.enabled:
            return
            
        self.enabled = False
        self.running = False
        
        try:
            if self.udp_socket:
                self.udp_socket.close()
                self.udp_socket = None
            if self.tcp_socket:
                self.tcp_socket.close()
                self.tcp_socket = None
            if self.websocket:
                self.websocket.close()
                self.websocket = None
            if self.mqtt_client:
                self.mqtt_client.disconnect()
                self.mqtt_client = None
                
        except Exception as e:
            print(f"關閉連接時出錯: {e}")
            
        self.status_changed.emit(self.protocol, "已斷開")
    
    def forward_data(self, mac: str, parsed_data: Dict[str, Any], battery: int, timestamp: int):
        """轉發IMU數據"""
        if not self.enabled:
            return
            
        # 構建數據包
        data_packet = {
            "timestamp": timestamp,
            "mac": mac,
            "data": {
                # 加速度 (g)
                "ax": round(parsed_data.get('ax', 0) * 16.0 / 32768.0, 6),
                "ay": round(parsed_data.get('ay', 0) * 16.0 / 32768.0, 6),
                "az": round(parsed_data.get('az', 0) * 16.0 / 32768.0, 6),
                # 陀螺儀 (°/s)
                "gx": round(parsed_data.get('gx', 0) * 2000.0 / 32768.0, 6),
                "gy": round(parsed_data.get('gy', 0) * 2000.0 / 32768.0, 6),
                "gz": round(parsed_data.get('gz', 0) * 2000.0 / 32768.0, 6),
                # 歐拉角 (°)
                "roll": round(parsed_data.get('roll', 0) * 180.0 / 32768.0, 6),
                "pitch": round(parsed_data.get('pitch', 0) * 180.0 / 32768.0, 6),
                "yaw": round(parsed_data.get('yaw', 0) * 180.0 / 32768.0, 6),
                # 四元數
                "q0": round(parsed_data.get('q0', 1.0), 6),
                "q1": round(parsed_data.get('q1', 0.0), 6),
                "q2": round(parsed_data.get('q2', 0.0), 6),
                "q3": round(parsed_data.get('q3', 0.0), 6),
            },
            "battery": battery
        }
        
        # 將數據放入隊列
        try:
            self.data_queue.put_nowait(data_packet)
        except queue.Full:
            self.error_count += 1
    
    def _start_udp(self):
        """啟動UDP連接"""
        self.udp_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        
    def _start_tcp(self):
        """啟動TCP連接"""
        self.tcp_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.tcp_socket.connect((self.host, self.port))
        
    def _start_websocket(self):
        """啟動WebSocket連接"""
        if not WEBSOCKET_AVAILABLE:
            raise ImportError("需要安裝 websocket-client: pip install websocket-client")
        url = f"ws://{self.host}:{self.port}"
        self.websocket = websocket.WebSocket()
        self.websocket.connect(url)

    def _start_mqtt(self):
        """啟動MQTT連接"""
        if not MQTT_AVAILABLE:
            raise ImportError("需要安裝 paho-mqtt: pip install paho-mqtt")
        self.mqtt_client = mqtt.Client()
        self.mqtt_client.connect(self.host, self.port, 60)
        self.mqtt_client.loop_start()
    
    def _send_loop(self):
        """數據發送循環"""
        while self.running:
            try:
                # 從隊列獲取數據，超時1秒
                data_packet = self.data_queue.get(timeout=1.0)
                json_data = json.dumps(data_packet, ensure_ascii=False)
                
                if self.protocol == "UDP":
                    self._send_udp(json_data)
                elif self.protocol == "TCP":
                    self._send_tcp(json_data)
                elif self.protocol == "WebSocket":
                    self._send_websocket(json_data)
                elif self.protocol == "MQTT":
                    self._send_mqtt(json_data)
                    
                self.sent_count += 1
                
            except queue.Empty:
                continue
            except Exception as e:
                self.error_count += 1
                print(f"發送數據時出錯: {e}")
                
    def _send_udp(self, data: str):
        """通過UDP發送數據"""
        if self.udp_socket:
            self.udp_socket.sendto(data.encode('utf-8'), (self.host, self.port))
            
    def _send_tcp(self, data: str):
        """通過TCP發送數據"""
        if self.tcp_socket:
            message = data + '\n'
            self.tcp_socket.send(message.encode('utf-8'))
            
    def _send_websocket(self, data: str):
        """通過WebSocket發送數據"""
        if self.websocket:
            self.websocket.send(data)
            
    def _send_mqtt(self, data: str):
        """通過MQTT發送數據"""
        if self.mqtt_client:
            self.mqtt_client.publish(self.mqtt_topic, data)
    
    def get_statistics(self) -> Dict[str, int]:
        """獲取統計信息"""
        return {
            "sent_count": self.sent_count,
            "error_count": self.error_count,
            "queue_size": self.data_queue.qsize()
        }
    
    def reset_statistics(self):
        """重置統計信息"""
        self.sent_count = 0
        self.error_count = 0
