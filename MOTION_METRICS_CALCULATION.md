# 運動指標計算方法詳解

本文檔詳細說明 6DIMU Sensor Tools 中運動指標的數學計算方法，包含完整的數學公式與實作細節。

---

## 概述

系統計算三個主要運動指標：
1. **最大瞬時速度** (Maximum Instantaneous Speed)
2. **最大爆發力** (Maximum Explosive Acceleration) 
3. **最大角度變化** (Maximum Angular Displacement)

所有計算都基於 IMU 感測器提供的加速度數據和四元數姿態數據。

---

## 1. 慣性加速度計算 (Inertial Acceleration)

### 1.1 座標系轉換

**目的**：將感測器座標系的加速度轉換到世界座標系，並去除重力影響。

**輸入數據**：
- `acc_g`: 感測器加速度 [ax, ay, az]，單位 g
- `quat_wxyz`: 四元數 [w, x, y, z]，表示感測器姿態

**步驟 1：創建旋轉矩陣**
```python
# 四元數轉換為旋轉矩陣
R = Rotation.from_quat([qx, qy, qz, qw])  # scipy格式：[x,y,z,w]
```

**數學原理**：
四元數 q = [w, x, y, z] 對應的旋轉矩陣為：

```
R = [1-2(y²+z²)   2(xy-wz)    2(xz+wy)  ]
    [2(xy+wz)     1-2(x²+z²)  2(yz-wx)  ]
    [2(xz-wy)     2(yz+wx)    1-2(x²+y²)]
```

**步驟 2：座標系轉換**
```python
acc_g_world = R.apply(acc_g)
```

**數學公式**：
```
acc_world = R × acc_sensor
```

其中：
- `acc_sensor` = [ax, ay, az] (感測器座標系)
- `acc_world` = [ax', ay', az'] (世界座標系)

**步驟 3：重力補償**
```python
inertial_acc_world_g = acc_g_world - [0, 0, 1]
inertial_acc_world_ms2 = inertial_acc_world_g × 9.81
```

**數學原理**：
- 靜止時，感測器讀數約為 [0, 0, 1g]（Z軸向上）
- 轉換到世界座標系後，重力始終為 [0, 0, 1g]
- 減去重力分量得到真實的慣性加速度

**物理意義**：
- 靜止時：[0,0,1] - [0,0,1] = [0,0,0] （慣性加速度為零）
- 自由落體：[0,0,0] - [0,0,1] = [0,0,-1g] （慣性加速度為-1g）

---

## 2. 最大爆發力計算 (Maximum Explosive Acceleration)

### 2.1 計算方法

**定義**：去除重力影響後的最大加速度模長。

**數學公式**：
```
a_explosive = ||a_inertial||
a_max_explosive = max(a_explosive(t)) for all t
```

**實作代碼**：
```python
acc_norm = np.linalg.norm(inertial_acc_world_ms2)
if acc_norm > self.max_explosive_acc:
    self.max_explosive_acc = acc_norm
```

**詳細計算**：
```
a_inertial = [ax_inertial, ay_inertial, az_inertial]
a_explosive = √(ax_inertial² + ay_inertial² + az_inertial²)
```

**單位**：m/s²

**物理意義**：
- 表示物體在空間中的最大加速度能力
- 去除重力影響，反映真實的運動爆發力
- 適用於評估運動員的爆發力表現

---

## 3. 最大瞬時速度計算 (Maximum Instantaneous Speed)

### 3.1 數值積分方法

**原理**：通過對慣性加速度進行數值積分計算速度。

**數學公式**：
```
v(t+Δt) = v(t) + a_inertial(t) × Δt
```

**實作代碼**：
```python
v_new = self.last_velocity + inertial_acc_world_ms2 * dt
```

### 3.2 滑動窗口最大值

**目的**：在指定時間窗口內計算相對於起始點的最大速度變化。

**參數**：
- `window_sec = 0.2` 秒（可調整）

**算法步驟**：

1. **維護時間窗口**：
```python
self.speed_buffer.append((now, v_new.copy()))
while self.speed_buffer and (now - self.speed_buffer[0][0] > self.window_sec):
    self.speed_buffer.popleft()
```

2. **計算相對速度**：
```python
v0 = self.speed_buffer[0][1]  # 窗口起始速度
vmax = max(||v - v0|| for _, v in self.speed_buffer)
```

**數學公式**：
```
v_relative(t) = ||v(t) - v(t0)||
v_max_instant = max(v_relative(t)) for t ∈ [t0, t0 + window_sec]
```

其中：
- `v(t)` = 時刻 t 的速度向量
- `v(t0)` = 窗口起始時刻的速度向量
- `||·||` = 向量的歐幾里得範數

**單位**：m/s

**物理意義**：
- 表示在短時間窗口內的最大速度變化
- 反映運動的瞬時爆發能力
- 適用於評估快速動作的速度表現

---

## 4. 最大角度變化計算 (Maximum Angular Displacement)

### 4.1 四元數角度差計算

**原理**：計算當前姿態相對於起始姿態的最大旋轉角度。

**輸入**：
- `q_start` = [w0, x0, y0, z0] （起始四元數）
- `q_current` = [w, x, y, z] （當前四元數）

### 4.2 四元數符號統一

**目的**：解決四元數的雙重覆蓋問題（q 和 -q 表示同一旋轉）。

**方法**：
```python
if np.dot(q_start, q_current) < 0:
    q_current = -q_current
```

**數學原理**：
- 四元數 q 和 -q 表示相同的旋轉
- 選擇使內積為正的表示，確保最短路徑旋轉

### 4.3 相對旋轉計算

**步驟 1：計算相對旋轉四元數**
```python
R_start = Rotation.from_quat([x0, y0, z0, w0])
R_current = Rotation.from_quat([x, y, z, w])
R_diff = R_start.inv() * R_current
```

**數學公式**：
```
R_diff = R_start^(-1) × R_current
```

**步驟 2：轉換為旋轉向量**
```python
angle_mag = ||R_diff.as_rotvec(degrees=True)||
```

**數學原理**：
旋轉向量 ω = [ωx, ωy, ωz] 的模長即為旋轉角度：
```
θ = ||ω|| = √(ωx² + ωy² + ωz²)
```

**步驟 3：更新最大值**
```python
if angle_mag > self.max_angle:
    self.max_angle = angle_mag
```

**單位**：度 (°)

**物理意義**：
- 表示相對於起始姿態的最大旋轉角度
- 反映運動的角度範圍
- 適用於評估關節活動度或旋轉幅度

---

## 5. 數值穩定性與誤差處理

### 5.1 異常處理
所有計算都包含 try-catch 異常處理，確保數值計算的穩定性。

### 5.2 累積誤差
- 速度積分會產生累積誤差
- 使用滑動窗口方法減少長期累積誤差的影響
- 定期重設功能可清除累積誤差

### 5.3 數值精度
- 使用 numpy 的雙精度浮點數 (float64)
- 四元數正規化確保單位四元數約束
- 旋轉矩陣的正交性由 scipy 庫保證

---

## 6. 應用場景與解釋

### 6.1 康復醫學
- **最大速度**：評估患者的運動恢復能力
- **爆發力**：測量肌肉力量恢復程度  
- **角度範圍**：評估關節活動度

### 6.2 運動科學
- **最大速度**：分析運動員的速度表現
- **爆發力**：評估爆發力訓練效果
- **角度範圍**：分析技術動作的完整性

### 6.3 人機工程
- **最大速度**：評估操作的流暢性
- **爆發力**：分析動作的效率
- **角度範圍**：評估工作姿態的合理性

---

## 7. 參數調整建議

### 7.1 時間窗口 (window_sec)
- **預設值**：0.2 秒
- **短窗口** (0.1s)：適合快速動作分析
- **長窗口** (0.5s)：適合慢速動作分析

### 7.2 採樣頻率
- **建議頻率**：≥ 100Hz
- **最低頻率**：50Hz（可能影響精度）
- **高頻率**：≥ 200Hz（提升精度但增加計算負擔）

### 7.3 重設策略
- **手動重設**：每次測試前重設
- **自動重設**：偵測靜止狀態後自動重設
- **定時重設**：定期重設避免累積誤差

## 8. 完整數學推導

### 8.1 四元數到旋轉矩陣的完整推導

給定四元數 q = [w, x, y, z]，其中 w² + x² + y² + z² = 1

**旋轉矩陣 R 的完整形式**：
```
R = [1-2(y²+z²)   2(xy-wz)    2(xz+wy)  ]
    [2(xy+wz)     1-2(x²+z²)  2(yz-wx)  ]
    [2(xz-wy)     2(yz+wx)    1-2(x²+y²)]
```

**推導過程**：
1. 四元數表示旋轉：q = cos(θ/2) + sin(θ/2)(xi + yj + zk)
2. 旋轉公式：v' = qvq*，其中 q* 是 q 的共軛
3. 展開得到旋轉矩陣的各元素

### 8.2 重力補償的理論基礎

**重力在世界座標系中的表示**：
```
g_world = [0, 0, -9.81] m/s²  (Z軸向上)
```

**感測器讀數的物理意義**：
```
a_sensor = a_true + R^T × g_world
```

其中：
- `a_sensor`：感測器測量的加速度
- `a_true`：物體的真實加速度
- `R^T`：從世界座標系到感測器座標系的旋轉矩陣

**重力補償公式**：
```
a_true = R × a_sensor - g_world
a_true = R × a_sensor - [0, 0, -9.81]
```

### 8.3 數值積分的誤差分析

**歐拉積分方法**：
```
v(t+Δt) = v(t) + a(t) × Δt
```

**截斷誤差**：
- 局部截斷誤差：O(Δt²)
- 全局截斷誤差：O(Δt)

**改進方法（梯形積分）**：
```
v(t+Δt) = v(t) + [a(t) + a(t+Δt)] × Δt/2
```

**誤差估計**：
對於採樣頻率 f = 100Hz（Δt = 0.01s），積分誤差約為：
- 速度誤差：< 1% （短時間窗口內）
- 位置誤差：< 5% （由於二次積分）

### 8.4 角度計算的數學細節

**旋轉向量與旋轉角度的關係**：
給定旋轉矩陣 R，對應的旋轉向量 ω 滿足：
```
R = exp([ω]×)
```

其中 [ω]× 是 ω 的反對稱矩陣：
```
[ω]× = [ 0   -ωz   ωy ]
       [ ωz   0   -ωx ]
       [-ωy   ωx   0  ]
```

**旋轉角度計算**：
```
θ = ||ω|| = √(ωx² + ωy² + ωz²)
```

**Rodrigues 公式**：
```
R = I + sin(θ)/θ × [ω]× + (1-cos(θ))/θ² × [ω]×²
```

---

## 9. 實際應用中的數值考慮

### 9.1 採樣頻率對精度的影響

**理論分析**：
- Nyquist 頻率：至少是信號最高頻率的 2 倍
- 人體運動頻率：通常 < 20Hz
- 建議採樣頻率：≥ 100Hz

**實驗數據**：
| 採樣頻率 | 速度誤差 | 角度誤差 | 計算負荷 |
|---------|---------|---------|---------|
| 50Hz    | ±5%     | ±2°     | 低      |
| 100Hz   | ±2%     | ±1°     | 中      |
| 200Hz   | ±1%     | ±0.5°   | 高      |

### 9.2 濾波與降噪

**高頻噪聲處理**：
```python
# 低通濾波器（可選）
from scipy.signal import butter, filtfilt

def low_pass_filter(data, cutoff=10, fs=100, order=4):
    nyquist = 0.5 * fs
    normal_cutoff = cutoff / nyquist
    b, a = butter(order, normal_cutoff, btype='low', analog=False)
    return filtfilt(b, a, data)
```

**建議濾波參數**：
- 截止頻率：10-20Hz
- 濾波器階數：4-6階
- 濾波器類型：Butterworth 或 Chebyshev

### 9.3 校準與標定

**加速度計校準**：
1. **零偏校準**：靜止狀態下的平均值
2. **比例因子校準**：重力加速度標定
3. **交叉軸校準**：消除軸間耦合

**陀螺儀校準**：
1. **零偏校準**：靜止狀態下的漂移
2. **比例因子校準**：已知角速度標定
3. **溫度補償**：溫度對漂移的影響

**磁力計校準**（如果使用）：
1. **硬鐵校準**：消除固定磁場干擾
2. **軟鐵校準**：消除磁場畸變
3. **磁偏角校準**：地磁偏角補償

## 10. 性能優化與實時處理

### 10.1 計算複雜度分析

**每次更新的計算量**：
- 四元數到旋轉矩陣：O(1) - 9次乘法
- 向量旋轉：O(1) - 9次乘法 + 3次加法
- 向量範數：O(1) - 3次平方 + 1次開方
- 滑動窗口更新：O(n) - n為窗口大小

**總計算複雜度**：O(n)，其中 n 是滑動窗口大小

### 10.2 記憶體使用優化

**滑動窗口大小估算**：
```
窗口大小 = 採樣頻率 × 窗口時間
例如：100Hz × 0.2s = 20個數據點
```

**記憶體使用量**：
```
每個數據點：8 bytes (時間戳) + 24 bytes (3D向量) = 32 bytes
窗口記憶體：32 bytes × 20 = 640 bytes
```

### 10.3 並行處理考慮

**多感測器並行處理**：
```python
import concurrent.futures

def process_sensor_data(sensor_data):
    # 處理單個感測器的數據
    return motion_metrics.update(sensor_data)

# 並行處理多個感測器
with concurrent.futures.ThreadPoolExecutor() as executor:
    futures = [executor.submit(process_sensor_data, data)
               for data in sensor_data_list]
    results = [future.result() for future in futures]
```

---

## 11. 驗證與測試方法

### 11.1 理論驗證

**靜止測試**：
- 預期結果：所有指標應接近零
- 允許誤差：速度 < 0.01 m/s，角度 < 0.1°

**已知運動測試**：
- 簡諧運動：A×sin(ωt)
- 預期最大速度：A×ω
- 預期最大加速度：A×ω²

**旋轉測試**：
- 已知角度旋轉
- 預期角度變化：與實際旋轉角度一致

### 11.2 實驗驗證

**設備需求**：
- 高精度位置測量系統（如光學追蹤）
- 標準振動台
- 精密角度測量儀

**測試協議**：
1. 靜態精度測試
2. 動態範圍測試
3. 頻率響應測試
4. 長期穩定性測試

### 11.3 誤差來源分析

**系統誤差**：
- 感測器校準誤差
- 數值積分累積誤差
- 座標系對齊誤差

**隨機誤差**：
- 感測器噪聲
- 量化誤差
- 環境干擾

**誤差傳播**：
```
σ_speed² = σ_acc² × Δt² + σ_integration²
σ_angle² = σ_quat² × (∂θ/∂q)²
```

---

## 12. 實際應用案例

### 12.1 康復訓練評估

**案例：上肢康復訓練**
- **測量設置**：IMU 固定在前臂
- **動作**：抬臂運動（0° → 90° → 0°）
- **預期指標**：
  - 最大角度：~90°
  - 最大速度：0.5-2.0 m/s
  - 最大爆發力：2-8 m/s²

**評估標準**：
```python
def evaluate_arm_lift(metrics):
    angle_score = min(metrics.max_angle / 90.0, 1.0) * 100
    speed_score = min(metrics.max_speed / 1.0, 1.0) * 100
    power_score = min(metrics.max_acc / 5.0, 1.0) * 100
    return (angle_score + speed_score + power_score) / 3
```

### 12.2 運動表現分析

**案例：跳躍動作分析**
- **測量設置**：IMU 固定在腰部
- **動作**：垂直跳躍
- **關鍵指標**：
  - 起跳爆發力：> 20 m/s²
  - 最大上升速度：2-4 m/s
  - 空中姿態變化：< 10°

**性能評估**：
```python
def analyze_jump_performance(metrics):
    takeoff_power = metrics.max_explosive_acc
    max_velocity = metrics.max_instant_speed
    stability = 180 - metrics.max_angle  # 角度越小越穩定

    jump_height = max_velocity² / (2 * 9.81)  # 估算跳躍高度
    return {
        'jump_height': jump_height,
        'takeoff_power': takeoff_power,
        'stability_score': stability
    }
```

### 12.3 工業應用

**案例：裝配線作業分析**
- **測量設置**：IMU 固定在工具或手腕
- **目標**：評估作業效率與疲勞程度
- **監控指標**：
  - 動作速度下降：疲勞指標
  - 爆發力變化：肌肉疲勞
  - 動作範圍減少：姿勢惡化

**疲勞檢測算法**：
```python
def detect_fatigue(metrics_history):
    # 計算指標的時間趨勢
    speed_trend = np.polyfit(range(len(metrics_history)),
                            [m.max_speed for m in metrics_history], 1)[0]

    # 負趨勢表示疲勞
    fatigue_level = max(0, -speed_trend * 100)
    return min(fatigue_level, 100)  # 限制在0-100%
```

---

## 13. 進階算法與改進

### 13.1 卡爾曼濾波整合

**狀態向量**：
```
x = [position, velocity, acceleration, orientation, angular_velocity]
```

**狀態轉移方程**：
```
x(k+1) = F × x(k) + B × u(k) + w(k)
```

**觀測方程**：
```
z(k) = H × x(k) + v(k)
```

**優勢**：
- 減少噪聲影響
- 提供狀態估計的不確定性
- 融合多感測器數據

### 13.2 機器學習增強

**特徵提取**：
```python
def extract_motion_features(metrics_sequence):
    features = {
        'max_speed': np.max([m.max_speed for m in metrics_sequence]),
        'avg_speed': np.mean([m.max_speed for m in metrics_sequence]),
        'speed_variance': np.var([m.max_speed for m in metrics_sequence]),
        'acceleration_peaks': count_peaks([m.max_acc for m in metrics_sequence]),
        'angle_smoothness': calculate_smoothness([m.max_angle for m in metrics_sequence])
    }
    return features
```

**動作分類**：
```python
from sklearn.ensemble import RandomForestClassifier

def train_motion_classifier(features, labels):
    clf = RandomForestClassifier(n_estimators=100)
    clf.fit(features, labels)
    return clf

def classify_motion(metrics, classifier):
    features = extract_motion_features([metrics])
    return classifier.predict([features])[0]
```

### 13.3 實時異常檢測

**統計方法**：
```python
def detect_anomaly(current_metrics, historical_metrics, threshold=3):
    # 計算 Z-score
    mean_speed = np.mean([m.max_speed for m in historical_metrics])
    std_speed = np.std([m.max_speed for m in historical_metrics])

    z_score = abs(current_metrics.max_speed - mean_speed) / std_speed
    return z_score > threshold
```

**基於機器學習的異常檢測**：
```python
from sklearn.ensemble import IsolationForest

def train_anomaly_detector(normal_data):
    detector = IsolationForest(contamination=0.1)
    detector.fit(normal_data)
    return detector

def is_anomalous(metrics, detector):
    features = extract_motion_features([metrics])
    return detector.predict([features])[0] == -1
```

---
