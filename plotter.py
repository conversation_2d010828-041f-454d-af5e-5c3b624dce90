import pyqtgraph as pg
from collections import deque
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QRadioButton, QButtonGroup,
    QCheckBox, QLabel, QDialog
)

class Plotter:
    def __init__(self, figsize=(3,2), dpi=100):
        self.data_length = 500
        self.data_buffers = {}
        self.lines = {}
        self.colors = {
            'ax': 'blue', 'ay': 'green', 'az': 'red',
            'gx': 'magenta', 'gy': 'orange', 'gz': 'brown',
            'roll': 'navy', 'pitch': 'darkorange', 'yaw': 'purple',
        }
        # 預設只有加速度三軸可見
        self.visible_dict = {k: (k in ['ax','ay','az']) for k in self.colors}
        # 當前顯示模式
        self.current_mode = 'acc'  # 'acc', 'gyro', 'euler'

        # 主Widget: 嵌入一個垂直layout，圖表在上，圖例在下
        self.main_widget = QWidget()
        self.main_layout = QVBoxLayout(self.main_widget)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(2)

        # 創建單一Y軸的圖表
        self.plot_widget = pg.PlotWidget()
        self.plot_widget.setBackground('w')
        self.plot_widget.showGrid(x=True, y=True)
        self.plot_widget.setTitle('IMU Data', color='k', size='10pt')
        self.plot_widget.setLabel('bottom', 'Samples', color='k')
        self.main_layout.addWidget(self.plot_widget)

        # 設置初始Y軸標籤
        self._update_y_axis_label()
        # 新增數據顯示模式選擇
        self.mode_group = QButtonGroup(self.main_widget)
        self.radio_acc = QRadioButton('加速度')
        self.radio_gyro = QRadioButton('角速度')
        self.radio_euler = QRadioButton('歐拉角')
        self.mode_group.addButton(self.radio_acc)
        self.mode_group.addButton(self.radio_gyro)
        self.mode_group.addButton(self.radio_euler)
        mode_layout = QHBoxLayout()
        mode_layout.addWidget(self.radio_acc)
        mode_layout.addWidget(self.radio_gyro)
        mode_layout.addWidget(self.radio_euler)
        self.main_layout.insertLayout(0, mode_layout)
        self.radio_acc.toggled.connect(self._on_mode_radio_changed)
        self.radio_gyro.toggled.connect(self._on_mode_radio_changed)
        self.radio_euler.toggled.connect(self._on_mode_radio_changed)

        # 創建自定義圖例
        self.legend_layout = QHBoxLayout()
        self.legend_layout.setSpacing(5)
        self.legend_layout.setContentsMargins(10, 0, 10, 0)
        self.legend_widget = QWidget()
        self.legend_widget.setLayout(self.legend_layout)
        self.legend_widget.setStyleSheet("background-color: transparent;")
        self.main_layout.addWidget(self.legend_widget)
        # 為每個數據創建圖例項和線條
        checkbox_style = """
            QCheckBox {
                background-color: transparent;
                padding: 0px;
                margin: 0px;
            }
            QCheckBox::indicator {
                width: 12px;
                height: 12px;
                border: 1px solid gray;
                border-radius: 2px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #2196F3;
                border-color: #2196F3;
            }
            QCheckBox::indicator:hover {
                border-color: #2196F3;
            }
        """
        self.checkbox_map = {}  # key:checkbox
        for key, color in self.colors.items():
            # 創建圖例項
            item_widget = QWidget()
            item_layout = QHBoxLayout()
            item_layout.setSpacing(2)
            item_layout.setContentsMargins(0, 0, 0, 0)
            checkbox = QCheckBox()
            # 預設只開啟加速度三軸
            checkbox.setChecked(key in ['ax','ay','az'])
            checkbox.setStyleSheet(checkbox_style)
            label = QLabel(key)
            label.setStyleSheet(f"color: {color}; background-color: transparent; padding: 0px; margin: 0px; font-size: 8pt;")
            label.setFixedHeight(15)
            item_layout.addWidget(checkbox)
            item_layout.addWidget(label)
            item_widget.setLayout(item_layout)
            self.legend_layout.addWidget(item_widget)
            checkbox.stateChanged.connect(lambda state, k=key: self._on_legend_checkbox_changed(k, state))

            # 創建線條（所有線條都在同一個plot widget上）
            self.lines[key] = self.plot_widget.plot([], [], pen=pg.mkPen(color=color, width=2))
            # 設置初始可見性
            self.lines[key].setVisible(key in ['ax','ay','az'])
            self.checkbox_map[key] = checkbox
        # 初始化數據緩衝區
        for key in self.colors:
            self.data_buffers[key] = deque(maxlen=self.data_length)

        # 設置初始範圍
        self.plot_widget.setXRange(0, self.data_length)

        # 預設radio button選加速度，並同步checkbox
        self.radio_acc.setChecked(True)
        self._on_mode_radio_changed()

    def get_canvas(self):
        return self.main_widget

    def _update_y_axis_label(self):
        """根據當前模式更新Y軸標籤"""
        if self.current_mode == 'acc':
            self.plot_widget.setLabel('left', 'Acceleration (g)', color='k')
        elif self.current_mode == 'gyro':
            self.plot_widget.setLabel('left', 'Angular Velocity (°/s)', color='k')
        elif self.current_mode == 'euler':
            self.plot_widget.setLabel('left', 'Euler Angle (°)', color='k')
        else:
            self.plot_widget.setLabel('left', 'Value', color='k')

    def update_plot_unit(self):
        """為了兼容性保留的方法"""
        self._update_y_axis_label()

    def add_data(self, data_dict):
        print("[DEBUG] 解析數據:", data_dict)  # 印出解析的數據
        for key, value in data_dict.items():
            if key not in self.colors:
                continue  # 忽略非曲線顯示欄位（如q0~q3）
            if not isinstance(value, (int, float)):
                continue
            if key in ['ax', 'ay', 'az']:
                converted_value = value * 16.0 / 32768.0
            elif key in ['gx', 'gy', 'gz']:
                converted_value = value * 2000.0 / 32768.0
            elif key in ['roll', 'pitch', 'yaw']:
                converted_value = value * 180.0 / 32768.0
            else:
                converted_value = value
            print(f"[DEBUG] 轉換後的值: {key} = {converted_value}")
            self.data_buffers.setdefault(key, deque(maxlen=self.data_length)).append(converted_value)

    def update_plot(self):
        min_y, max_y = None, None
        visible_count = 0

        # 更新所有可見的線條
        for key, buffer in self.data_buffers.items():
            if len(buffer) > 0 and self.visible_dict.get(key, False):
                x = list(range(len(buffer)))
                y = list(buffer)
                self.lines[key].setData(x, y)
                self.lines[key].setVisible(True)
                visible_count += 1

                # 計算Y軸範圍
                cur_min = min(y)
                cur_max = max(y)
                min_y = cur_min if min_y is None else min(min_y, cur_min)
                max_y = cur_max if max_y is None else max(max_y, cur_max)

                # 調試信息
                if visible_count == 1:  # 只打印第一個可見線條的信息
                    print(f"[DEBUG] 顯示線條 {key}: 數據點數={len(y)}, 範圍=[{cur_min:.3f}, {cur_max:.3f}]")
            else:
                # 隱藏不可見的線條
                self.lines[key].setData([], [])
                self.lines[key].setVisible(False)

        # 調試信息
        if visible_count == 0:
            print(f"[DEBUG] 沒有可見的線條! visible_dict={self.visible_dict}")
        else:
            print(f"[DEBUG] 共有 {visible_count} 條可見線條")

        # 自動調整Y軸範圍
        if min_y is not None and max_y is not None:
            margin = (max_y - min_y) * 0.1
            if margin == 0:
                margin = abs(min_y) * 0.1 if min_y != 0 else 0.1
            self.plot_widget.setYRange(min_y - margin, max_y + margin)

        # 設置X軸範圍
        self.plot_widget.setXRange(0, self.data_length)

    def set_curve_visibility(self, visible_dict):
        """設置曲線可見性"""
        self.visible_dict = visible_dict
        for key, line in self.lines.items():
            is_visible = self.visible_dict.get(key, False)
            line.setVisible(is_visible)

    def set_axis_unit(self, unit):
        self.y_unit = unit
        self.plot_widget.setLabel('left', f'Value ({self.y_unit})')

    def _on_legend_checkbox_changed(self, key, state):
        """處理圖例checkbox變化"""
        self.visible_dict[key] = bool(state)
        self.lines[key].setVisible(bool(state))
        
    def show_large_plot(self, parent=None):
        dlg = QDialog(parent)
        dlg.setWindowTitle("放大圖")
        vbox = QVBoxLayout(dlg)
        big_plot = pg.PlotWidget()
        
        # 使用與主圖表相同的背景色
        background_color = self.plot_widget.background.color().name()
        text_color = 'k' if background_color == '#ffffff' else 'w'
        big_plot.setBackground(background_color)
        big_plot.showGrid(x=True, y=True)
        big_plot.setTitle('IMU Data', color=text_color, size='12pt')
        big_plot.setLabel('left', f'Value ({self.y_unit})', color=text_color)
        big_plot.setLabel('bottom', 'Samples', color=text_color)
        
        # 為大圖創建圖例
        legend_layout = QHBoxLayout()
        legend_layout.setSpacing(10)
        legend_widget = QWidget()
        legend_widget.setLayout(legend_layout)
        legend_widget.setStyleSheet("background-color: transparent;")
        
        # 只顯示當前可見的數據
        for key, color in self.colors.items():
            if self.visible_dict.get(key, False):
                # 繪製數據
                pen = pg.mkPen(color=color, width=2)
                data = list(self.data_buffers[key])
                if data:
                    big_plot.plot(list(range(len(data))), data, pen=pen)
                
                # 創建圖例項
                item_widget = QWidget()
                item_layout = QHBoxLayout()
                item_layout.setContentsMargins(0, 0, 0, 0)

                label = QLabel(key)
                label.setStyleSheet(f"color: {color}; background-color: transparent;")
                
                item_layout.addWidget(label)
                item_widget.setLayout(item_layout)
                legend_layout.addWidget(item_widget)
        
        # 將圖例添加到場景
        proxy = big_plot.scene().addWidget(legend_widget)
        proxy.setPos(10, 10)
        
        big_plot.setYRange(-40000, 40000)
        big_plot.setXRange(0, self.data_length)
        
        vbox.addWidget(big_plot)
        dlg.setLayout(vbox)
        dlg.exec()

    def clear(self):
        """清除所有數據"""
        for key in self.data_buffers:
            self.data_buffers[key].clear()
            self.lines[key].setData([], [])

    def set_background(self, color):
        """設置背景顏色"""
        self.plot_widget.setBackground(color)
        # 根據背景色調整文字顏色
        text_color = 'k' if color == 'w' else 'w'
        self.plot_widget.setTitle('IMU Data', color=text_color, size='10pt')
        self.plot_widget.setLabel('bottom', 'Samples', color=text_color)
        # 重新設置Y軸標籤（保持當前模式的標籤）
        self._update_y_axis_label()
        
        # 更新所有圖例標籤的顏色
        for i in range(self.legend_layout.count()):
            item = self.legend_layout.itemAt(i).widget()
            if item:
                label = item.findChild(QLabel)
                if label:
                    current_style = label.styleSheet()
                    color_start = current_style.find("color: ") + 7
                    color_end = current_style.find(";", color_start)
                    color_value = current_style[color_start:color_end]
                    new_style = current_style.replace(
                        f"color: {color_value}",
                        f"color: {color_value}"  # 保持原有顏色不變
                    )
                    label.setStyleSheet(new_style) 





    def _on_mode_radio_changed(self):
        """處理模式切換"""
        if self.radio_acc.isChecked():
            self.current_mode = 'acc'
            # 只顯示加速度
            for k in self.colors:
                self.checkbox_map[k].setChecked(k in ['ax','ay','az'])
        elif self.radio_gyro.isChecked():
            self.current_mode = 'gyro'
            # 只顯示角速度
            for k in self.colors:
                self.checkbox_map[k].setChecked(k in ['gx','gy','gz'])
        elif self.radio_euler.isChecked():
            self.current_mode = 'euler'
            # 只顯示歐拉角
            for k in self.colors:
                self.checkbox_map[k].setChecked(k in ['roll','pitch','yaw'])

        # 更新Y軸標籤
        self._update_y_axis_label()

        # 強制刷新曲線顯示
        self.set_curve_visibility({k: self.checkbox_map[k].isChecked() for k in self.colors})