# trend_analysis_tab.py
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QFileDialog, QGroupBox, QGridLayout, QListWidget, QListWidgetItem,
    QSplitter, QTextEdit, QComboBox, QDateEdit, QCheckBox
)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QFont
from modern_widgets import ModernButton, SidebarSection

class TrendAnalysisTab(QWidget):
    """趨勢分析頁籤"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.data_files = []  # 儲存多個數據檔案
        self._setup_ui()
        # 添加示例數據用於UI測試
        self.add_sample_files()
        self.populate_sample_insights()
    
    def _setup_ui(self):
        """設置UI佈局"""
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(15)
        
        # 左側控制面板
        control_panel = self._create_control_panel()
        main_layout.addWidget(control_panel, 1)
        
        # 右側顯示區域
        display_area = self._create_display_area()
        main_layout.addWidget(display_area, 3)
    
    def _create_control_panel(self):
        """創建左側控制面板"""
        panel = QWidget()
        panel.setMaximumWidth(320)
        layout = QVBoxLayout(panel)
        layout.setSpacing(15)
        
        # 數據管理區域
        data_section = SidebarSection("數據管理")
        data_layout = QVBoxLayout()
        
        # 檔案列表
        data_layout.addWidget(QLabel("已載入的數據檔案:"))
        self.file_list = QListWidget()
        self.file_list.setMaximumHeight(150)
        self.file_list.setStyleSheet("""
            QListWidget {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid #3a3a4e;
                border-radius: 4px;
                padding: 5px;
            }
            QListWidget::item {
                padding: 5px;
                border-bottom: 1px solid #3a3a4e;
                color: #ccc;
            }
            QListWidget::item:selected {
                background: rgba(0, 212, 255, 0.3);
            }
        """)
        data_layout.addWidget(self.file_list)
        
        # 檔案操作按鈕
        file_btn_layout = QHBoxLayout()
        self.add_file_btn = ModernButton("添加檔案", "primary")
        self.remove_file_btn = ModernButton("移除", "danger")
        self.remove_file_btn.setEnabled(False)
        file_btn_layout.addWidget(self.add_file_btn)
        file_btn_layout.addWidget(self.remove_file_btn)
        data_layout.addLayout(file_btn_layout)
        
        self.clear_all_btn = ModernButton("清除全部", "warning")
        data_layout.addWidget(self.clear_all_btn)
        
        data_section.setLayout(data_layout)
        layout.addWidget(data_section)
        
        # 分析設置區域
        analysis_section = SidebarSection("分析設置")
        analysis_layout = QVBoxLayout()
        
        # 時間範圍設置
        time_layout = QVBoxLayout()
        time_layout.addWidget(QLabel("分析時間範圍:"))
        
        date_layout = QHBoxLayout()
        date_layout.addWidget(QLabel("從:"))
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setCalendarPopup(True)
        date_layout.addWidget(self.start_date)
        time_layout.addLayout(date_layout)
        
        date_layout2 = QHBoxLayout()
        date_layout2.addWidget(QLabel("到:"))
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        date_layout2.addWidget(self.end_date)
        time_layout.addLayout(date_layout2)
        
        analysis_layout.addLayout(time_layout)
        
        # 指標選擇
        metrics_layout = QVBoxLayout()
        metrics_layout.addWidget(QLabel("分析指標:"))
        
        self.speed_check = QCheckBox("最大速度")
        self.speed_check.setChecked(True)
        self.acc_check = QCheckBox("最大加速度")
        self.acc_check.setChecked(True)
        self.force_check = QCheckBox("最大力度")
        self.force_check.setChecked(True)
        
        metrics_layout.addWidget(self.speed_check)
        metrics_layout.addWidget(self.acc_check)
        metrics_layout.addWidget(self.force_check)
        analysis_layout.addLayout(metrics_layout)
        
        # 趨勢分析類型
        trend_layout = QVBoxLayout()
        trend_layout.addWidget(QLabel("趨勢分析類型:"))
        self.trend_type = QComboBox()
        self.trend_type.addItems([
            "線性趨勢",
            "移動平均",
            "指數平滑",
            "季節性分析"
        ])
        trend_layout.addWidget(self.trend_type)
        analysis_layout.addLayout(trend_layout)
        
        # 開始分析按鈕
        self.analyze_trend_btn = ModernButton("開始趨勢分析", "primary")
        self.analyze_trend_btn.setEnabled(False)
        analysis_layout.addWidget(self.analyze_trend_btn)
        
        analysis_section.setLayout(analysis_layout)
        layout.addWidget(analysis_section)
        
        # 報告生成區域
        report_section = SidebarSection("報告生成")
        report_layout = QVBoxLayout()
        
        self.generate_report_btn = ModernButton("生成趨勢報告", "success")
        self.generate_report_btn.setEnabled(False)
        report_layout.addWidget(self.generate_report_btn)
        
        self.export_data_btn = ModernButton("導出分析數據", "warning")
        self.export_data_btn.setEnabled(False)
        report_layout.addWidget(self.export_data_btn)
        
        report_section.setLayout(report_layout)
        layout.addWidget(report_section)
        
        layout.addStretch()
        return panel
    
    def _create_display_area(self):
        """創建右側顯示區域"""
        display = QWidget()
        layout = QVBoxLayout(display)
        layout.setSpacing(15)
        
        # 標題
        title = QLabel("趨勢分析結果")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setStyleSheet("color: #00d4ff; padding: 10px;")
        layout.addWidget(title)
        
        # 使用分割器分為上下兩部分
        splitter = QSplitter(Qt.Orientation.Vertical)
        
        # 上半部：趨勢圖表
        chart_group = QGroupBox("趨勢圖表")
        chart_layout = QVBoxLayout(chart_group)
        
        self.trend_chart_placeholder = QLabel("趨勢圖表將在此顯示")
        self.trend_chart_placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.trend_chart_placeholder.setStyleSheet("""
            background: rgba(255, 255, 255, 0.05);
            border: 2px dashed #666;
            border-radius: 8px;
            padding: 40px;
            color: #666;
            font-size: 14px;
        """)
        self.trend_chart_placeholder.setMinimumHeight(300)
        chart_layout.addWidget(self.trend_chart_placeholder)
        
        splitter.addWidget(chart_group)
        
        # 下半部：統計摘要和洞察
        summary_widget = QWidget()
        summary_layout = QHBoxLayout(summary_widget)
        
        # 統計摘要
        stats_group = QGroupBox("統計摘要")
        stats_layout = QVBoxLayout(stats_group)
        
        self.stats_display = QTextEdit()
        self.stats_display.setMaximumHeight(200)
        self.stats_display.setReadOnly(True)
        self.stats_display.setStyleSheet("""
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid #3a3a4e;
            border-radius: 4px;
            padding: 10px;
            color: #ccc;
            font-family: 'Arial', sans-serif;
            font-size: 12px;
        """)
        stats_layout.addWidget(self.stats_display)
        
        summary_layout.addWidget(stats_group)
        
        # 分析洞察
        insights_group = QGroupBox("分析洞察")
        insights_layout = QVBoxLayout(insights_group)
        
        self.insights_display = QTextEdit()
        self.insights_display.setMaximumHeight(200)
        self.insights_display.setReadOnly(True)
        self.insights_display.setStyleSheet("""
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid #3a3a4e;
            border-radius: 4px;
            padding: 10px;
            color: #ccc;
            font-family: 'Arial', sans-serif;
            font-size: 12px;
        """)
        insights_layout.addWidget(self.insights_display)
        
        summary_layout.addWidget(insights_group)
        
        splitter.addWidget(summary_widget)
        
        # 設置分割器比例
        splitter.setSizes([400, 200])
        layout.addWidget(splitter)
        
        return display
    
    def add_sample_files(self):
        """添加示例檔案（用於UI測試）"""
        sample_files = [
            "2024-01-15_訓練記錄.csv",
            "2024-01-22_訓練記錄.csv", 
            "2024-01-29_訓練記錄.csv",
            "2024-02-05_訓練記錄.csv",
            "2024-02-12_訓練記錄.csv"
        ]
        
        for filename in sample_files:
            item = QListWidgetItem(filename)
            item.setToolTip(f"檔案路徑: /data/{filename}")
            self.file_list.addItem(item)
        
        self.update_button_states()
    
    def update_button_states(self):
        """更新按鈕狀態"""
        has_files = self.file_list.count() > 0
        has_selection = self.file_list.currentItem() is not None
        has_multiple_files = self.file_list.count() >= 2
        
        self.remove_file_btn.setEnabled(has_selection)
        self.clear_all_btn.setEnabled(has_files)
        self.analyze_trend_btn.setEnabled(has_multiple_files)
        self.generate_report_btn.setEnabled(has_multiple_files)
        self.export_data_btn.setEnabled(has_multiple_files)
        
        if not has_multiple_files:
            self.analyze_trend_btn.setText(f"需要至少2個檔案 ({self.file_list.count()}/2)")
        else:
            self.analyze_trend_btn.setText("開始趨勢分析")
    
    def populate_sample_insights(self):
        """填充示例分析洞察"""
        stats_text = """
📊 統計摘要:
• 分析期間: 2024-01-15 至 2024-02-12
• 總訓練次數: 5 次
• 平均訓練間隔: 7 天

📈 指標趨勢:
• 最大速度: 4.2 → 5.8 m/s (+38%)
• 最大加速度: 11.5 → 14.2 m/s² (+23%)
• 最大力度: 1.8 → 2.4 g (+33%)
        """
        
        insights_text = """
🔍 關鍵洞察:
• 整體表現呈現穩定上升趨勢
• 速度改善最為顯著，顯示爆發力訓練效果良好
• 加速度指標在第3週出現小幅下降，可能與訓練強度調整有關
• 建議保持當前訓練頻率，適度增加力量訓練

⚠️ 注意事項:
• 第4次訓練數據異常，建議檢查設備狀態
• 建議增加恢復期監測
        """
        
        self.stats_display.setPlainText(stats_text)
        self.insights_display.setPlainText(insights_text)
