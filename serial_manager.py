import serial
import serial.tools.list_ports
import threading
import time
import re

class SerialManager:
    def __init__(self, on_data_callback=None, on_disconnect_callback=None):
        self.ser = None
        self.thread = None
        self.running = False
        self.on_data_callback = on_data_callback
        self.on_disconnect_callback = on_disconnect_callback
        self.buffer = ''
        self.mac_set = set()
        self.lock = threading.Lock()

    @staticmethod
    def list_ports():
        return [port.device for port in serial.tools.list_ports.comports()]

    def connect(self, port, baudrate=115200):
        self.disconnect()
        try:
            self.ser = serial.Serial(port, baudrate, timeout=0.1)
            self.running = True
            self.thread = threading.Thread(target=self._read_loop, daemon=True)
            self.thread.start()
            return True
        except Exception as e:
            self.ser = None
            self.running = False
            return False

    def disconnect(self):
        self.running = False
        if self.ser:
            try:
                self.ser.close()
            except Exception:
                pass
            self.ser = None
        if self.thread:
            self.thread = None

    def send_nus_tx(self, mac, data):
        if self.ser and self.ser.is_open:
            cmd = f'NUS_TX:{mac},{data};\n'
            self.ser.write(cmd.encode())

    def _read_loop(self):
        try:
            while self.running and self.ser and self.ser.is_open:
                try:
                    raw = self.ser.read(4096)
                    if not raw:
                        time.sleep(0.01)
                        continue
                    self.buffer += raw.decode(errors='ignore')
                    while True:
                        match = re.search(r'NUS_RX:([0-9A-Fa-f:]+),([0-9A-Fa-f ]+);', self.buffer)
                        if not match:
                            break
                        mac = match.group(1)
                        hexdata = match.group(2).strip()
                        self.mac_set.add(mac)
                        try:
                            data_bytes = bytes.fromhex(hexdata)
                        except Exception:
                            self.buffer = self.buffer[match.end():]
                            continue
                        if self.on_data_callback:
                            self.on_data_callback(mac, data_bytes)
                        self.buffer = self.buffer[match.end():]
                except Exception as e:
                    if self.on_disconnect_callback:
                        self.on_disconnect_callback(str(e))
                    self.disconnect()
                    break
        finally:
            self.running = False
            if self.on_disconnect_callback:
                self.on_disconnect_callback('Serial disconnected')

    def get_mac_list(self):
        with self.lock:
            return list(self.mac_set) 