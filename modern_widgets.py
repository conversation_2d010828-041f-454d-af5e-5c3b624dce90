# modern_widgets.py
# 現代化UI組件庫

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QFrame, QProgressBar, QComboBox, QLineEdit, QGroupBox
)
from PySide6.QtCore import Qt, Signal, QPropertyAnimation, QEasingCurve, QRect
from PySide6.QtGui import QPainter, QColor, QLinearGradient, QBrush, QPen
import math

class StatusIndicator(QWidget):
    """狀態指示器組件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(20, 20)
        self._status = 'disconnected'  # connected, disconnected, connecting
        self._animation = QPropertyAnimation(self, b"geometry")
        self._animation.setDuration(1000)
        self._animation.setEasingCurve(QEasingCurve.Type.InOutQuad)
        
    def set_status(self, status):
        """設置狀態：connected, disconnected, connecting"""
        self._status = status
        self.update()
        
        if status == 'connecting':
            self._start_pulse_animation()
        else:
            self._animation.stop()
    
    def _start_pulse_animation(self):
        """開始脈衝動畫"""
        self._animation.setStartValue(self.geometry())
        end_rect = QRect(self.x()-2, self.y()-2, 24, 24)
        self._animation.setEndValue(end_rect)
        self._animation.setLoopCount(-1)  # 無限循環
        self._animation.start()
    
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 根據狀態選擇顏色
        if self._status == 'connected':
            color = QColor(0, 255, 136)  # 綠色
        elif self._status == 'connecting':
            color = QColor(255, 170, 0)  # 橙色
        else:
            color = QColor(255, 71, 87)  # 紅色
        
        # 繪製圓形指示器
        painter.setBrush(QBrush(color))
        painter.setPen(QPen(color.darker(150), 2))
        painter.drawEllipse(2, 2, 16, 16)

class MetricCard(QWidget):
    """運動指標卡片組件"""
    
    def __init__(self, title, unit="", max_value=100, parent=None):
        super().__init__(parent)
        self.title = title
        self.unit = unit
        self.max_value = max_value
        self.current_value = 0
        self._setup_ui()
        
    def _setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(6)

        # 標題
        title_label = QLabel(self.title)
        title_label.setObjectName("metric_label")
        title_label.setWordWrap(True)  # 允許文字換行
        title_label.setMinimumHeight(20)
        layout.addWidget(title_label)

        # 數值顯示
        self.value_label = QLabel("-- " + self.unit)
        self.value_label.setObjectName("metric_value")
        self.value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.value_label.setWordWrap(True)  # 允許文字換行
        self.value_label.setMinimumHeight(25)
        layout.addWidget(self.value_label)

        # 進度條
        self.progress_bar = QProgressBar()
        self.progress_bar.setMaximum(int(self.max_value * 100))
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setFixedHeight(6)
        layout.addWidget(self.progress_bar)
        
        # 設置樣式
        self.setStyleSheet("""
            MetricCard {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid #3a3a4e;
                border-radius: 8px;
                min-height: 80px;
            }
            QLabel {
                font-size: 11px;
            }
            QLabel#metric_value {
                font-size: 13px;
                font-weight: bold;
            }
            QProgressBar {
                border: none;
                border-radius: 3px;
                background: rgba(255, 255, 255, 0.1);
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #00d4ff, stop:1 #667eea);
                border-radius: 3px;
            }
        """)
    
    def update_value(self, value):
        """更新數值"""
        self.current_value = value
        self.value_label.setText(f"{value:.2f} {self.unit}")
        
        # 更新進度條
        progress = min(int((value / self.max_value) * 100), 100)
        self.progress_bar.setValue(progress * 100)

class ModernComboBox(QComboBox):
    """現代化下拉選單"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet("""
            ModernComboBox {
                background: #e0f7fa;
                color: #1565c0;
                border: 1px solid #90caf9;
                border-radius: 6px;
                padding: 4px 8px;
                min-height: 20px;
                max-height: 28px;
            }
            ModernComboBox:hover {
                border: 1px solid #00d4ff;
            }
            ModernComboBox::drop-down {
                border: none;
                width: 20px;
            }
            ModernComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #1565c0;
            }
            ModernComboBox QAbstractItemView {
                background: #e0f7fa;
                color: #1565c0;
                border: 1px solid #90caf9;
                border-radius: 6px;
                selection-background-color: #00d4ff;
            }
        """)

class ModernButton(QPushButton):
    """現代化按鈕"""
    
    def __init__(self, text, button_type="primary", parent=None):
        super().__init__(text, parent)
        self.button_type = button_type
        self._setup_style()
        
    def _setup_style(self):
        if self.button_type == "primary":
            bg_color = "#00d4ff"
            hover_color = "#00b8e6"
        elif self.button_type == "success":
            bg_color = "#00ff88"
            hover_color = "#00e67a"
        elif self.button_type == "danger":
            bg_color = "#ff4757"
            hover_color = "#ff3742"
        elif self.button_type == "warning":
            bg_color = "#ffaa00"
            hover_color = "#e69500"
        else:
            bg_color = "#3a3a4e"
            hover_color = "#4a4a5e"
            
        self.setStyleSheet(f"""
            ModernButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {bg_color}, stop:1 {hover_color});
                color: white;
                border: none;
                border-radius: 6px;
                padding: 6px 16px;
                font-size: 13px;
                font-weight: bold;
                min-height: 26px;
            }}
            ModernButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {hover_color}, stop:1 {bg_color});
            }}
            ModernButton:pressed {{
                background: {hover_color};
            }}
            ModernButton:disabled {{
                background: #3a3a4e;
                color: #b8b8cc;
            }}
        """)

class SidebarSection(QGroupBox):
    """側邊欄區段組件"""
    
    def __init__(self, title, parent=None):
        super().__init__(title, parent)
        self.setStyleSheet("""
            SidebarSection {
                font-size: 15px;
                font-weight: bold;
                color: #00d4ff;
                border: 1px solid #3a3a4e;
                border-radius: 6px;
                margin-top: 6px;
                padding-top: 6px;
            }
            SidebarSection::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 6px 0 6px;
                background: #1a1a2e;
                font-size: 15px;
                font-weight: bold;
            }
        """)

class ConnectionPanel(QWidget):
    """連接控制面板"""
    
    connect_requested = Signal(str)
    disconnect_requested = Signal()
    refresh_requested = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_ui()
        # 設置標題字體與粗體
        title = self.findChild(QLabel, "sidebar_title")
        if title:
            title.setStyleSheet("font-size: 15px; font-weight: bold; color: #00d4ff;")
        
    def _setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 10, 12, 10)
        layout.setSpacing(6)
        
        # 標題
        title = QLabel("連接控制")
        title.setObjectName("sidebar_title")
        layout.addWidget(title)
        
        # 狀態指示器
        status_layout = QHBoxLayout()
        self.status_indicator = StatusIndicator()
        self.status_label = QLabel("未連接")
        self.status_label.setObjectName("status_disconnected")
        status_layout.addWidget(self.status_indicator)
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        layout.addLayout(status_layout)
        
        # COM端口選擇
        layout.addWidget(QLabel("COM端口:"))
        self.port_combo = ModernComboBox()
        layout.addWidget(self.port_combo)
        
        # 按鈕組
        button_layout = QHBoxLayout()
        self.refresh_btn = ModernButton("刷新", "primary")
        self.connect_btn = ModernButton("連接", "success")
        self.disconnect_btn = ModernButton("斷開", "danger")
        
        button_layout.addWidget(self.refresh_btn)
        button_layout.addWidget(self.connect_btn)
        button_layout.addWidget(self.disconnect_btn)
        layout.addLayout(button_layout)
        
        # 連接信號
        self.refresh_btn.clicked.connect(self.refresh_requested.emit)
        self.connect_btn.clicked.connect(self._on_connect)
        self.disconnect_btn.clicked.connect(self.disconnect_requested.emit)
        
        # 初始狀態
        self.disconnect_btn.setEnabled(False)
        
    def _on_connect(self):
        port = self.port_combo.currentText()
        if port:
            self.connect_requested.emit(port)
    
    def set_ports(self, ports):
        """設置可用端口列表"""
        current = self.port_combo.currentText()
        self.port_combo.clear()
        self.port_combo.addItems(ports)
        if current in ports:
            self.port_combo.setCurrentText(current)
    
    def set_connection_status(self, connected):
        """設置連接狀態"""
        if connected:
            self.status_indicator.set_status('connected')
            self.status_label.setText("已連接")
            self.status_label.setObjectName("status_connected")
            self.connect_btn.setEnabled(False)
            self.disconnect_btn.setEnabled(True)
        else:
            self.status_indicator.set_status('disconnected')
            self.status_label.setText("未連接")
            self.status_label.setObjectName("status_disconnected")
            self.connect_btn.setEnabled(True)
            self.disconnect_btn.setEnabled(False)
        
        # 重新應用樣式
        self.status_label.style().unpolish(self.status_label)
        self.status_label.style().polish(self.status_label)
